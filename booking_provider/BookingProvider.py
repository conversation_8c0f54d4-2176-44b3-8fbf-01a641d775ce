from abc import ABC, abstractmethod
from datetime import date
from typing import Any, Dict, List, Optional

from fastapi import HTTPException
from requests import RequestException, Session

from lib.supabase_client import supabase_client
from models import (
    Appointment,
    AppointmentForm,
    AppointmentStepSearch,
    CombinedAppointments,
    Doctor,
    InboundConfig,
    Motive,
    NewAppointment,
    NewPatient,
    Patient,
    UpdatePatient,
)


class BookingProvider(ABC):
    def __init__(self, type: str, config_id: str):
        self.type = type
        self.config_id = config_id
        self.base_url = ""
        self.session = Session()
        self.company_data = supabase_client.get_company_data_by_config_id(config_id)

    def get_booking_provider_type(self) -> str:
        """Returns the type of the booking provider."""
        return self.type

    def get_with_body(self, endpoint: str, json: Optional[dict] = None):
        """Effectue une requête GET avec un corps vers l'API EDL."""
        try:
            response = self.session.get(self.base_url + endpoint, json=json)
            response.raise_for_status()
            return response.json()
        except RequestException as e:
            raise Exception(f"Erreur lors de la requête GET: {e}")

    def get(self, endpoint: str, params: Optional[dict] = None):
        """Effectue une requête GET vers l'API EDL."""
        try:
            response = self.session.get(self.base_url + endpoint, params=params)
            response.raise_for_status()
            return response.json()
        except RequestException as e:
            raise Exception(f"Erreur lors de la requête GET: {e}")

    def post(self, endpoint: str, json: Optional[dict] = None):
        """Effectue une requête POST vers l'API EDL."""
        try:
            response = self.session.post(self.base_url + endpoint, json=json)
            response.raise_for_status()
            return response.json()
        except RequestException as e:
            raise Exception(f"Erreur lors de la requête POST: {e}")

    def put(self, endpoint: str, json: Optional[dict] = None):
        """Effectue une requête PUT vers l'API EDL."""
        try:
            response = self.session.put(self.base_url + endpoint, json=json)
            response.raise_for_status()
            return response.json()
        except RequestException as e:
            raise Exception(f"Erreur lors de la requête PUT: {e}")

    @abstractmethod
    def health_check(self) -> bool:
        """Checks if the booking provider is operational."""
        pass

    # TODO: we should not have code in an abstract method
    @abstractmethod
    def get_availabilities(  # type: ignore
        self,
        doctors: List[Doctor],
        motive: Motive,
        day: Optional[date],
        practice_id: Optional[str] = None,
    ) -> List[Appointment]:
        """Fetches available slots for booking."""
        if not doctors:
            raise HTTPException(status_code=400, detail="No doctors provided.")
        if not motive:
            raise HTTPException(status_code=400, detail="No motive provided.")
        pass

    @abstractmethod
    def get_availabilities_mulitiple_motives(
        self,
        steps: List[AppointmentStepSearch],
        day: date,
        min_wait_between_steps: Optional[int] = 0,
        max_wait_between_steps: Optional[int] = 30,
    ) -> List[CombinedAppointments]:
        pass

    @abstractmethod
    def get_original_motives(self, speciality_id: Optional[int] = None) -> List[dict]:
        """Fetches motives for booking."""
        pass

    @abstractmethod
    def get_original_specialities(self) -> List[dict]:
        """Fetches specialities for booking."""
        pass

    @abstractmethod
    def get_original_doctors(self) -> List[dict]:
        """Fetches doctors for booking."""
        pass

    @abstractmethod
    def generate_config(self) -> InboundConfig:
        """Generates configuration for the booking provider."""
        pass

    @abstractmethod
    def create_patient(self, patient_data: NewPatient) -> Patient:
        """Creates a new patient in the booking system."""
        pass

    @abstractmethod
    def search_patient(
        self,
        patient_id: Optional[str] = None,
        phone_number: Optional[str] = None,
        first_name: Optional[str] = None,
        last_name: Optional[str] = None,
        birthdate: Optional[str] = None,
        raw: bool = False,
    ) -> List[Patient]:
        """Search for a patient based on various parameters."""
        pass

    @abstractmethod
    def get_patient(self, patient_id: str) -> Patient:
        """Fetches a patient by ID."""
        pass

    @abstractmethod
    def update_patient(self, patient: UpdatePatient) -> Patient:
        """Updates an existing patient."""
        pass

    @abstractmethod
    def get_appointments_of_patient(self, patient_id: str) -> List[AppointmentForm]:
        """Fetches appointments for a given patient."""
        pass

    @abstractmethod
    def get_appointment(self, appointment_id: str) -> AppointmentForm:
        """Fetches an appointment by ID."""
        pass

    @abstractmethod
    def create_appointment(self, new_appointment: NewAppointment) -> AppointmentForm:
        """Creates a new appointment."""
        pass

    @abstractmethod
    def update_appointment(
        self, appointment_id: str, data: Dict[str, Any]
    ) -> AppointmentForm:
        """Updates an existing appointment."""
        pass

    @abstractmethod
    def confirm_appointment(
        self, appointment_id: str, notes: str = ""
    ) -> AppointmentForm:
        """Confirms an existing appointment."""
        pass

    @abstractmethod
    def cancel_appointment(
        self, appointment_id: str, notes: str = ""
    ) -> AppointmentForm:
        """Cancels an existing appointment."""
        pass

    @abstractmethod
    def get_appointments_of_day(
        self, agenda_ids: List[int], day: date
    ) -> List[AppointmentForm]:
        """Fetches appointments for a specific day and agenda."""
        pass
