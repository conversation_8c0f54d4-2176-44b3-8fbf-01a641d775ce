from datetime import date, datetime
from typing import Dict, List, Optional
from zoneinfo import ZoneInfo

from fastapi import HTTP<PERSON>x<PERSON>
from loguru import logger
from requests import RequestException, Session

from booking_provider.BookingProvider import BookingProvider
from constants import DOCTOLIB_SECRET_SCRAP_TOKEN, DOCTOLIB_SECRET_SCRAP_URL
from lib.supabase_client import supabase_client
from models import (Appointment, AppointmentForm, AppointmentStepSearch,
                    CombinedAppointments, Doctor, InboundConfig, Motive,
                    NewAppointment, NewPatient, Patient, UpdatePatient)
from utils.doctors import find_doctor_by_id_company_data
from utils.motives import find_visit_motive


class DoctolibSecretScrap(BookingProvider):
    """Client pour l'API EDL."""

    def __init__(self, config: str):
        super().__init__("DOCTOLIB", config)
        if not config:
            raise ValueError("Config must be provided for DoctolibSecretScrap.")

        self.base_url = DOCTOLIB_SECRET_SCRAP_URL
        self.api_version = "/api/v2"
        self.session = Session()
        self.session.headers.update(
            {"Authorization": f"Bearer {DOCTOLIB_SECRET_SCRAP_TOKEN}"},
        )
        self.session.headers.update({"Config-id": config})

    def health_check(self) -> bool:
        return self.get("/health") == {"status": "ok"}

    def get_availabilities(
        self,
        doctors: List[Doctor],
        motive: Motive,
        day: Optional[date],
        practice_id: Optional[str] = None,
    ) -> List[Appointment]:
        endpoint = f"{self.api_version}/appointments/availabilities"
        super().get_availabilities(doctors, motive, day, practice_id)

        body = {
            "agenda_id": [doctor.id for doctor in doctors],
            "visit_motive_id": motive.id,
            "day": day.strftime("%Y-%m-%d") if day else None,
            "practice_id": practice_id,
        }

        response = self.post(endpoint, json=body)

        practice_id = (
            self.company_data.inbound_config_file.practice_id
            if self.company_data and self.company_data.inbound_config_file
            else None
        )

        return [
            Appointment(
                **item,
                visit_motive_id=motive.id,
                booking_provider=self.type,
                practice_id=practice_id,
            )
            for item in response
        ]

    def get_availabilities_mulitiple_motives(
        self,
        steps: List[AppointmentStepSearch],
        day: date,
        min_wait_between_steps: Optional[int] = 0,
        max_wait_between_steps: Optional[int] = 3,
    ) -> List[CombinedAppointments]:
        """Fetches combined appointments"""
        endpoint = f"{self.api_version}/appointments/availabilities-steps"

        if len(steps) != 2:
            raise ValueError("Steps length should be two")

        agenda_ids_1 = steps[0].agenda_ids
        agenda_ids_2 = steps[1].agenda_ids
        visit_motive_1 = steps[0].visit_motive_id
        visit_motive_2 = steps[1].visit_motive_id
        body = {
            "config": self.config_id,
            "day": day.strftime("%Y-%m-%d"),
            "agenda_ids_1": agenda_ids_1,
            "agenda_ids_2": agenda_ids_2,
            "visit_motive_id_1": visit_motive_1,
            "visit_motive_id_2": visit_motive_2,
            "min_wait_between_steps": min_wait_between_steps,
            "max_wait_between_steps": max_wait_between_steps,
        }

        resp = self.get_with_body(
            endpoint,
            body,
        )

        if not resp or not isinstance(resp, list):
            logger.error("Did not return a list")
            logger.error(resp)
            return []

        list_raw_combined_appointments = resp
        combined_appointments: List[CombinedAppointments] = []
        for raw_combined_appointment in list_raw_combined_appointments:
            start_date = (
                datetime.strptime(
                    raw_combined_appointment.get("patient_arrival_date"),
                    "%Y-%m-%dT%H:%M:%S.%f%z",
                )
                if raw_combined_appointment.get("patient_arrival_date")
                else None
            )
            end_date = (
                datetime.strptime(
                    raw_combined_appointment.get("patient_departure_date"),
                    "%Y-%m-%dT%H:%M:%S.%f%z",
                )
                if raw_combined_appointment.get("patient_departure_date")
                else None
            )

            if not (start_date and end_date):
                raise ValueError("missing start and end date")

            if start_date and start_date.tzinfo is None:
                start_date = start_date.replace(tzinfo=ZoneInfo("Europe/Paris"))
            if end_date and end_date.tzinfo is None:
                end_date = end_date.replace(tzinfo=ZoneInfo("Europe/Paris"))

            raw_steps = raw_combined_appointment.get("steps", [])
            result_steps: List[Appointment] = []
            for raw_step in raw_steps:
                agenda_id = raw_step.get("agenda_id")
                doctor = find_doctor_by_id_company_data(self.company_data, agenda_id)
                if not raw_step["practitioner_agenda_id"]:
                    del raw_step["practitioner_agenda_id"]

                result_steps.append(
                    Appointment(
                        **raw_step,
                        medecin=doctor.name if doctor else None,
                        external_agenda_id=doctor.external_id if doctor else None,
                        practitioner_agenda_id=(
                            doctor.practitioner_id if doctor else None
                        ),
                        practice_id=self.company_data.inbound_config_file.practice_id,
                        booking_provider=self.type,
                    )
                )

            combined_appointments.append(
                CombinedAppointments(
                    start_date=start_date,
                    end_date=end_date,
                    steps=result_steps,
                    booking_provider=self.type,
                    num_motives=len(result_steps),
                )
            )

        return combined_appointments

    def get_original_motives(self, speciality_id: Optional[int] = None) -> List[dict]:
        """Fetches motives for booking."""
        endpoint = f"{self.api_version}/configs/generate"
        raw_inbound_configs = self.post(
            endpoint, json={"config_id": self.config_id, "json_only": True}
        )
        if not raw_inbound_configs:
            raise ValueError("Failed to retrieve inbound config.")

        raw_company_data = next(
            (
                item
                for item in raw_inbound_configs
                if item.get("practice_id")
                == self.company_data.inbound_config_file.practice_id
            ),
            None,
        )
        if not raw_company_data:
            raise ValueError("Failed to retrieve company data.")
        raw_motives = raw_company_data.get("visit-motives-categories", [])
        if not raw_motives:
            raise ValueError("Failed to retrieve visit motives.")

        return raw_motives

    def get_original_specialities(self) -> List[dict]:
        """Fetches specialities for booking."""
        endpoint = f"{self.api_version}/configs/generate"
        raw_inbound_configs = self.post(
            endpoint, json={"config_id": self.config_id, "json_only": True}
        )
        if not raw_inbound_configs:
            raise ValueError("Failed to retrieve inbound config.")

        raw_company_data = next(
            (
                item
                for item in raw_inbound_configs
                if item.get("practice_id")
                == self.company_data.inbound_config_file.practice_id
            ),
            None,
        )
        if not raw_company_data:
            raise ValueError("Failed to retrieve company data.")
        raw_specialities = raw_company_data.get("specialities", [])
        if not raw_specialities:
            raise ValueError("Failed to retrieve visit specialities.")

        return raw_specialities

    def get_original_doctors(self) -> List[dict]:
        """Fetches doctors for booking."""
        endpoint = f"{self.api_version}/configs/generate"
        raw_inbound_configs = self.post(
            endpoint, json={"config_id": self.config_id, "json_only": True}
        )
        if not raw_inbound_configs:
            raise ValueError("Failed to retrieve inbound config.")

        raw_company_data = next(
            (
                item
                for item in raw_inbound_configs
                if item.get("practice_id")
                == self.company_data.inbound_config_file.practice_id
            ),
            None,
        )
        if not raw_company_data:
            raise ValueError("Failed to retrieve company data.")
        raw_doctors = raw_company_data.get("calendars", [])
        if not raw_doctors:
            raise ValueError("Failed to retrieve visit doctors.")

        return raw_doctors

    def generate_config(self) -> InboundConfig:
        """Generates configuration for the booking provider."""
        endpoint = f"{self.api_version}/configs/generate"
        raw_inbound_configs = self.post(
            endpoint, json={"config_id": self.config_id, "json_only": True}
        )
        if not raw_inbound_configs:
            raise ValueError("Failed to retrieve inbound config.")

        raw_company_data = next(
            (
                item
                for item in raw_inbound_configs
                if item.get("practice_id")
                == self.company_data.inbound_config_file.practice_id
            ),
            None,
        )
        if not raw_company_data:
            raise ValueError("Failed to retrieve company data.")

        return InboundConfig(**raw_company_data)

    def create_patient(self, patient_data: NewPatient) -> Patient:
        """Creates a new patient in the booking system."""
        endpoint = "/api/v1/add-patient"
        try:
            response = self.post(endpoint, json=patient_data.model_dump())
        except RequestException as e:
            raise HTTPException(status_code=500, detail=str(e))

        if not response:
            raise HTTPException(status_code=400, detail="Failed to create patient.")

        return Patient(**response, is_new_patient=True)

    def search_patient(
        self,
        patient_id: Optional[str] = None,
        phone_number: Optional[str] = None,
        first_name: Optional[str] = None,
        last_name: Optional[str] = None,
        birthdate: Optional[str] = None,
        raw: bool = False,
    ) -> List[Patient]:
        """Search for a patient based on various parameters."""
        found_patient = None
        if patient_id:
            endpoint = f"{self.api_version}/users/{patient_id}"
            response = self.get(endpoint)
            raw_person = response if response else None
            found_patient = (
                Patient(**raw_person, is_new_patient=False) if raw_person else None
            )

        endpoint = f"{self.api_version}/users/search-patient"
        body = {
            "phone_number": phone_number,
            "first_name": first_name,
            "last_name": last_name,
            "birthdate": birthdate,
        }
        if found_patient:
            body = {
                "phone_number": found_patient.phone_number,
            }

        response = self.get_with_body(endpoint, json=body)

        if not response:
            return []

        patients = []
        for raw_patient in response:
            if found_patient and raw_patient.get("id") != found_patient.id:
                continue

            raw_patient_id = raw_patient.get("id", "")
            if not raw_patient_id:
                continue

            tmp_appointments: List[AppointmentForm] = []
            for appointment in raw_patient.get("appointments", []):
                motive = find_visit_motive(
                    self.company_data, str(appointment.get("visit_motive_id"))
                )
                if not motive or not motive.speciality_id:
                    logger.warning(
                        f"Visit motive not found or invalid for appointment: {appointment}"
                    )
                    continue

                doctor = find_doctor_by_id_company_data(
                    self.company_data, appointment.get("agenda_id")
                )

                tmp_appointments.append(
                    AppointmentForm(
                        **appointment,
                        practitioner_id=doctor.practitioner_id if doctor else None,
                        patient_id=raw_patient_id,
                        speciality_id=motive.speciality_id,
                        instructions=motive.instructions,
                        booking_provider=self.type,
                    )
                )

            raw_patient["appointments"] = tmp_appointments

            next_appointment = next(
                (
                    appt
                    for appt in tmp_appointments
                    if appt.id == raw_patient.get("next_appointment").get("id")
                ),
                None,
            )
            raw_patient["next_appointment"] = next_appointment
            last_appointment = next(
                (
                    appt
                    for appt in tmp_appointments
                    if appt.id == raw_patient.get("last_appointment").get("id")
                ),
                None,
            )
            raw_patient["last_appointment"] = last_appointment
            patient = Patient(
                **raw_patient,
                is_new_patient=False,
            )
            patients.append(patient)

        return patients

    def get_patient(self, patient_id: str) -> Patient:
        """Fetches a patient by ID."""
        patients = self.search_patient(patient_id=patient_id)
        if not patients:
            raise HTTPException(status_code=404, detail="Patient not found.")

        if len(patients) > 1:
            logger.warning(
                f"Multiple patients found with ID {patient_id}. Returning the first one."
            )

        return patients[0]

    def update_patient(self, patient: UpdatePatient) -> Patient:
        """Updates an existing patient."""
        endpoint = "/api/v1/patients"

        data_to_update = {
            k: v for k, v in patient.model_dump().items() if v is not None
        }

        resp = self.put(
            endpoint,
            json={"config": self.config_id, "patient_data": data_to_update},
        )
        if not resp:
            raise HTTPException(status_code=404, detail="Patient not found.")

        return Patient(**resp, is_new_patient=False)

    def get_appointments_of_patient(self, patient_id: str) -> List[AppointmentForm]:
        """Fetches appointments for a given patient."""
        patients = self.search_patient(patient_id=patient_id)
        if not patients:
            return []

        if len(patients) > 0:
            return patients[0].appointments

        patient = patients[0]

        return patient.appointments

    def get_appointment(
        self, appointment_id: str, is_sub_appt: bool = False
    ) -> AppointmentForm:
        """Fetches an appointment by ID."""
        endpoint = f"{self.api_version}/appointments"
        body = {"id": appointment_id, "config": self.config_id}
        response = self.get_with_body(endpoint, json=body)
        if not response:
            raise HTTPException(status_code=404, detail="Appointment not found.")

        motive = find_visit_motive(
            self.company_data, str(response.get("visit_motive_id"))
        )
        if not motive or not motive.speciality_id:
            logger.warning(
                f"Visit motive not found or invalid for appointment: {response}"
            )
            raise HTTPException(status_code=400, detail="Invalid visit motive.")

        doctor = find_doctor_by_id_company_data(
            self.company_data, response.get("agenda_id")
        )

        ### TODO Implement when doing double booking
        set_appointment_ids = response.get("set_appointment_ids", [])
        steps = []
        if not is_sub_appt and set_appointment_ids and len(set_appointment_ids) > 0:
            for set_appointment_id in set_appointment_ids:
                appointment_info = self.get_appointment(
                    set_appointment_id, is_sub_appt=True
                )
                steps.append(appointment_info.model_dump())

        del response["steps"]

        return AppointmentForm(
            **response,
            speciality_id=motive.speciality_id,
            booking_provider=self.type,
            practitioner_id=doctor.practitioner_id if doctor else None,
            instructions=motive.instructions,
            steps=steps,
        )

    def create_appointment(self, new_appointment: NewAppointment) -> AppointmentForm:
        """Creates a new appointment."""
        endpoint = f"{self.api_version}/appointments"

        if new_appointment.end_date is None:
            raise HTTPException(
                status_code=400,
                detail="End date is required for appointment creation in Doctolib.",
            )

        body = {"config": self.config_id, **new_appointment.model_dump()}
        response = self.post(endpoint, json=body)
        if not response:
            raise HTTPException(status_code=404, detail="Appointment not found.")

        doctor = find_doctor_by_id_company_data(
            self.company_data, response.get("agenda_id")
        )

        new_created_appointment = AppointmentForm(
            **response,
            external_agenda_id=doctor.external_id if doctor else "NOT_FOUND",
            booking_provider=self.type,
        )
        if new_created_appointment.steps:
            for step in new_created_appointment.steps:
                doctor = find_doctor_by_id_company_data(
                    self.company_data, step.agenda_id
                )
                step.external_agenda_id = doctor.external_id if doctor else "NOT_FOUND"

        return new_created_appointment

    def update_appointment(self, appointment_id: str, data: dict) -> AppointmentForm:
        """Updates an existing appointment."""
        endpoint = f"{self.api_version}/appointments"
        body = {"id": appointment_id, "config": self.config_id, **data}
        response = self.put(endpoint, json=body)
        if not response:
            raise HTTPException(status_code=404, detail="Appointment not found.")

        motive = find_visit_motive(
            self.company_data, str(response.get("visit_motive_id"))
        )
        if not motive or not motive.speciality_id:
            logger.warning(
                f"Visit motive not found or invalid for appointment: {response}"
            )
            raise HTTPException(status_code=400, detail="Invalid visit motive.")

        return AppointmentForm(
            **response,
            speciality_id=motive.speciality_id,
            booking_provider=self.type,
        )

    def confirm_appointment(
        self, appointment_id: str, notes: str = ""
    ) -> AppointmentForm:
        """Confirms an existing appointment."""
        confirmation_params = supabase_client.get_confirmation_params(self.config_id)

        # Override with provided notes if given
        if notes:
            confirmation_params.confirmation_note = notes

        # Build request body
        body = {
            "id": appointment_id,
            "config": self.config_id,
            "custom_fields_values": {},
        }

        # Add custom fields if configured
        if (
            confirmation_params.confirmation_field_key
            and confirmation_params.confirmation_field_value
        ):
            body["custom_fields_values"] = {
                confirmation_params.confirmation_field_key: confirmation_params.confirmation_field_value
            }

        # Add note if present
        if confirmation_params.confirmation_note:
            body["note"] = confirmation_params.confirmation_note

        return self.update_appointment(appointment_id, body)

    def cancel_appointment(
        self,
        appointment_id: str,
        notes: str = "",
    ) -> AppointmentForm:
        """Cancels an existing appointment."""
        endpoint = f"{self.api_version}/appointments/cancel"
        body = {"id": appointment_id, "config": self.config_id, "notes": notes}
        response = self.put(endpoint, json=body)
        if not response:
            raise HTTPException(status_code=404, detail="Appointment not found.")

        return self.get_appointment(appointment_id)

    def get_appointments_of_day(
        self, agenda_ids: List[int], day: date
    ) -> List[AppointmentForm]:
        """Fetches appointments for a specific day and agenda."""
        endpoint = "/api/v1/day"
        day_str = day.strftime("%Y-%m-%d")
        body = {"agenda_id": agenda_ids, "day": day_str}
        response = self.get_with_body(endpoint, json=body)

        if not response:
            logger.warning(
                f"No appointments found for day {day_str} and agendas {agenda_ids}."
            )
            return []

        raw_appts = response
        doctors: Dict[int, Doctor] = {}
        for raw_appt in raw_appts:
            motive_id = raw_appt.get("visit_motive_id")
            motive = find_visit_motive(self.company_data, str(motive_id))
            if motive:
                raw_appt["speciality_id"] = motive.speciality_id
                raw_appt["visit_motive_name"] = motive.name
                raw_appt["instructions"] = motive.instructions

            if not doctors.get(raw_appt.get("agenda_id")):
                doctor = find_doctor_by_id_company_data(
                    self.company_data, raw_appt.get("agenda_id")
                )
                if doctor:
                    doctors[raw_appt["agenda_id"]] = doctor

            doctor = doctors.get(raw_appt.get("agenda_id"))
            if doctor:
                raw_appt["practitioner_id"] = str(doctor.practitioner_id)

        return [
            AppointmentForm(**appt, booking_provider=self.type) for appt in raw_appts
        ]
