from zoneinfo import ZoneInfo

import dateutil.parser


def doctolib_transformer_appointment(cls, values):
    # Transform the appointment data for Doctolib
    values["start_date"] = dateutil.parser.parse(values["start_date"]).astimezone(
        ZoneInfo("Europe/Paris")
    )
    values["end_date"] = dateutil.parser.parse(values["end_date"]).astimezone(
        ZoneInfo("Europe/Paris")
    )

    values["equipment_agenda_id"] = (
        str(values.get("equipment_agenda_id"))
        if values.get("equipment_agenda_id")
        else None
    )
    values["practitioner_agenda_id"] = (
        str(values.get("practitioner_agenda_id"))
        if values.get("practitioner_agenda_id")
        else None
    )
    values["external_agenda_id"] = (
        str(values["agenda_id"]) if values.get("agenda_id") else None
    )

    return values


def doctolib_transformer_appointment_form(cls, values):
    if values.get("equipment_agenda_id"):
        values["equipment_agenda_id"] = str(values.get("equipment_agenda_id"))

    return values
