from fastapi import HTTPException
from loguru import logger

from lib.supabase_client import supabase_client

from .BookingProvider import BookingProvider
from .DOCTOLIB_SECRET_SCRAP.doctolib_scrap_client import DoctolibSecretScrap
from .EDL.edl_client import EDLApiClient

config_booking_provider: dict[str, BookingProvider] = {}


def get_booking_provider(config_id: str) -> BookingProvider | None:
    """
    Returns the booking provider instance based on the config.
    """
    try:
        booking_provider = None
        if config_id in config_booking_provider.keys():
            logger.info(f"Using cached booking provider for config ID: {config_id}")
            booking_provider = config_booking_provider[config_id]
        else:
            company_data = supabase_client.get_company_data_by_config_id(config_id)
            booking_provider_type = company_data.booking_provider

            if booking_provider_type == "EDL":
                if not company_data.search_url:
                    raise ValueError("Search URL is missing in company data.")
                booking_provider = EDLApiClient(company_data.search_url, config_id)

            if booking_provider_type == "DOCTOLIB":
                booking_provider = DoctolibSecretScrap(config_id)

            if not booking_provider:
                logger.error(f"Unsupported booking provider: {booking_provider_type}")
                raise ValueError(
                    f"Unsupported booking provider: {booking_provider_type}"
                )

            config_booking_provider[config_id] = booking_provider

        return booking_provider

    except ValueError as e:
        raise HTTPException(
            status_code=400,
            detail=f"Error occurred while getting booking provider: {str(e)}",
        )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error occurred while getting booking provider: {str(e)}",
        )
