from datetime import date, datetime
from typing import List, Optional
from zoneinfo import ZoneInfo

from loguru import logger
from requests import Session

from booking_provider.BookingProvider import BookingProvider
from models import (Appointment, AppointmentForm, AppointmentFormStep,
                    AppointmentStepSearch, CombinedAppointments, Doctor,
                    InboundConfig, Motive, NewAppointment, NewPatient, Patient,
                    Speciality, UpdatePatient)
from utils.appointments import find_consecutive_combinations
from utils.doctors import find_doctor_by_id_company_data
from utils.motives import find_visit_motive


class EDLApiClient(BookingProvider):
    """Client pour l'API EDL."""

    def __init__(self, base_url: str, config: str):
        super().__init__("EDL", config)
        if not base_url:
            raise ValueError("L'URL de base de l'API EDL ne peut pas être vide.")

        self.base_url = base_url + "/Application/api/External"
        self.session = Session()

    def health_check(self) -> bool:
        """Checks if the booking provider is operational."""
        try:
            raise NotImplementedError("Health check not implemented")
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return False

    def get_original_motives(self, speciality_id: Optional[int] = None) -> List[dict]:
        """
        Fetch motives from the EDL service, optionally filtered by speciality ID.
        """
        endpoint = "/GetListeExamensFromTypeExamen"

        motives = []
        if speciality_id:
            resp = self.post(endpoint, json={"id": speciality_id})
            if resp and resp.get("success"):
                return resp.get("data", [])
            else:
                raise Exception(
                    f"Error retrieving motives for speciality {speciality_id}: {resp.get('errorMessages', 'Unknown error')}"
                )

        specialities = self.get_original_specialities()
        motives = []
        for speciality in specialities:
            speciality_code = speciality.get("code")
            resp = self.post(endpoint, json={"id": speciality_code})
            if resp and resp.get("success"):
                motives.extend(resp.get("data", []))
            else:
                raise Exception(
                    f"Error retrieving motives for speciality {speciality_code}: {resp.get('errorMessages', 'Unknown error')}"
                )

        return motives

    def get_original_specialities(self) -> List[dict]:
        endpoint = "/GetListeTypesExamens"
        resp = self.get(endpoint)
        if resp and resp.get("success"):
            data = resp.get("data", [])
            return data
        else:
            raise Exception(
                f"Error retrieving specialities: {resp.get('errorMessages', 'Unknown error')}"
            )

    def get_original_doctors(self) -> List[dict]:
        endpoint = "/GetListeMedecins"
        resp = self.get(endpoint)
        if resp and resp.get("success"):
            return resp.get("data", [])

        else:
            raise Exception(
                f"Error retrieving doctors: {resp.get('errorMessages', 'Unknown error')}"
            )

    def get_original_practices(self, speciality_id: Optional[int] = None):
        """
        Fetch practices from the EDL service.
        """
        endpoint = "/GetListeSitesFromTypeExamen"

        if speciality_id:
            resp = self.post(endpoint, json={"id": speciality_id})
            if resp and resp.get("success"):
                return resp.get("data", [])
            else:
                raise Exception(
                    f"Error retrieving practices for speciality {speciality_id}: {resp.get('errorMessages', 'Unknown error')}"
                )

        specialities = self.get_original_specialities()
        practices = []
        for speciality in specialities:
            speciality_code = speciality.get("code")
            resp = self.post(endpoint, json={"id": speciality_code})
            if resp and resp.get("success"):
                practices.extend(resp.get("data", []))
            else:
                raise Exception(
                    f"Error retrieving practices for speciality {speciality_code}: {resp.get('errorMessages', 'Unknown error')}"
                )

        return practices

    def generate_config(self):
        """
        Generate the configuration for the EDL service.
        """
        # config_num = self.config_id.replace("config", "")
        practices = self.get_original_practices()
        unique_practices = list(set([practice["code"] for practice in practices]))

        if not unique_practices or len(unique_practices) > 1:
            raise ValueError("Multiple practices found. Please implement the feature.")

        specialities = self.get_original_specialities()
        motives = self.get_original_motives()
        doctors = self.get_original_doctors()

        specialities = [
            Speciality(id=int("".join(str(ord(c)) for c in spec["code"])), **spec)
            for spec in specialities
        ]

        motives = [
            Motive(id=int("".join(str(ord(c)) for c in motive["code"])), **motive)
            for motive in motives
        ]

        for motive in motives:
            speciality = next(
                (
                    spec
                    for spec in specialities
                    if spec.external_id == motive.speciality_external_id
                ),
                None,
            )
            if not speciality:
                raise ValueError(
                    f"Speciality not found for external ID {motive.speciality_external_id}"
                )

            motive.type = speciality.name if speciality else None
            motive.speciality_id = int(speciality.id) if speciality else None

        for doctor in doctors:
            doctor["external_id"] = doctor.pop("id", None)

        doctors = [
            Doctor(
                id=int("".join(str(ord(c)) for c in doctor["external_id"])),
                speciality_id=0,
                **doctor,
            )
            for doctor in doctors
        ]
        organization_id = self.base_url
        patient_base_id = self.base_url
        practice_id = unique_practices[0]

        config = {
            "specialities": specialities,
            "visit-motives-categories": motives,
            "calendars": doctors,
            "organization_id": organization_id,
            "patient_base_id": patient_base_id,
            "practice_id": practice_id,
        }

        return InboundConfig(**config)

    def get_appointments_of_patient(self, patient_id: str) -> List[AppointmentForm]:
        """
        Fetch appointments for a given patient from the EDL service.
        """
        if not patient_id:
            raise ValueError("Patient ID must be provided.")

        endpoint = "/GetExamensPatient"
        resp = self.post(
            endpoint,
            json={"IdPatient": patient_id},
        )
        if resp.get("success"):
            raw_appointments = resp.get("data", [])
            appointments = []
            for raw_appointment in raw_appointments:
                details_appointments = self.__get_appointment(
                    raw_appointment.get("idExamen")
                )
                motive = find_visit_motive(
                    self.company_data,
                    raw_appointment.get("codeExamen"),
                    skip_llm_search=True,
                )
                if not motive:
                    logger.warning(
                        f"Visit motive not found for code {raw_appointment.get('codeExamen')}"
                    )

                doctor = find_doctor_by_id_company_data(
                    self.company_data, raw_appointment.get("codeMedecin")
                )

                details_appointments["speciality_id"] = (
                    motive.speciality_id if motive else 0
                )

                appointments.append(
                    AppointmentForm(
                        **details_appointments,
                        practitioner_id=doctor.practitioner_id if doctor else None,
                        patient_id=raw_appointment.get("idPatient"),
                        visit_motive_id=motive.id if motive else 0,
                        visit_motive_name=motive.name if motive else "NOT_FOUND",
                        instructions=motive.instructions if motive else "",
                        booking_provider="EDL",
                    )
                )

            appointments.sort(key=lambda a: a.start_date)
            return appointments

        raise Exception(
            f"Error fetching appointments: {resp.get('errorMessages', 'Unknown error')}"
        )

    def create_patient(self, patient_data: NewPatient) -> Patient:
        """
        Creates a new patient in the EDL system.
        Returns the created patient's ID.
        """
        raise NotImplementedError(
            "Creating a new patient is not implemented in the EDL API client."
        )

    def get_patient(self, patient_id: str) -> Patient:
        patients = self.search_patient(patient_id=patient_id)

        if not patients:
            raise ValueError("Patient not found.")

        if len(patients) > 1:
            raise ValueError("Ambiguous patient search result.")

        return patients[0]

    def search_patient(
        self,
        patient_id: Optional[str] = None,
        phone_number: Optional[str] = None,
        first_name: Optional[str] = None,
        last_name: Optional[str] = None,
        birthdate: Optional[str] = None,
        raw: bool = False,
    ):
        """
        Fetch motives from the EDL service, optionally filtered by speciality ID.
        """
        endpoint = "/SearchPatients"

        if birthdate:
            raise ValueError("Birthdate search is not supported in EDL.")

        if not any([patient_id, phone_number, first_name, last_name]):
            raise ValueError("At least one search parameter must be provided.")

        numeroDossier = patient_id or ""
        telephone = phone_number or ""
        prenom = first_name or ""
        nom = last_name or ""

        resp = self.post(
            endpoint,
            json={
                "NumeroDossier": numeroDossier,
                "Telephone": telephone,
                "Prenom": prenom,
                "Nom": nom,
            },
        )
        possible_patients = resp.get("data", [])

        if raw:
            return possible_patients

        if not possible_patients:
            return []

        possible_patients = [
            Patient(**patient) for patient in possible_patients if patient
        ]
        for patient in possible_patients:
            appointments = self.get_appointments_of_patient(patient.id)
            patient.appointments = appointments
            if appointments:
                patient.next_appointment = appointments[0]
                patient.last_appointment = (
                    appointments[-1] if len(appointments) > 1 else appointments[0]
                )

        # sort by number of appointments
        possible_patients.sort(key=lambda p: len(p.appointments), reverse=True)

        if resp.get("success"):
            return possible_patients
        else:
            raise Exception(
                f"Error searching patients: {resp.get('errorMessages', 'Unknown error')}"
            )

    def update_patient(self, patient: UpdatePatient) -> Patient:
        """Updates an existing patient."""
        raise NotImplementedError(
            "Updating a patient is not implemented in the EDL API client."
        )

    def get_availabilities(
        self,
        doctors: List[Doctor],
        motive: Motive,
        day: Optional[date],
        practice_id: Optional[str] = None,
    ) -> List[Appointment]:
        endpoint = "/GetCreneauxFromCriteria"

        day_str = (
            day.strftime("%Y-%m-%d") if day else datetime.now().strftime("%Y-%m-%d")
        )

        body = {
            "typeExamen": motive.speciality_external_id,
            "codeExamen": motive.external_id,
            "dateDebut": day_str,
        }

        if len(doctors) == 1:
            body["codeMedecin"] = doctors[0].external_id
        if practice_id:
            body["codeSite"] = practice_id

        try:
            resp = self.post(endpoint, json=body)
            data = resp.get("data", [])
            if not data:
                return []

            data = resp.get("data", [])
            if not data:
                return []

            appts = [
                Appointment(**item, visit_motive_id=motive.id, booking_provider="EDL")
                for item in data
            ]

            for appt in appts:
                if not appt.external_agenda_id:
                    logger.warning("Appointment has no external doctor ID, skipping.")
                    continue

                doctor = find_doctor_by_id_company_data(
                    self.company_data, appt.external_agenda_id
                )
                if doctor:
                    appt.medecin = doctor.name
                    appt.practitioner_agenda_id = doctor.practitioner_id
                    appt.agenda_id = doctor.id

            if len(doctors) > 1:
                doctors_ids = [doc.id for doc in doctors]
                appts = [appt for appt in appts if appt.agenda_id in doctors_ids]

            return appts
        except Exception as e:
            raise Exception(
                f"Error fetching availabilities for motive {motive.name} on day {day}: {str(e)}"
            )

    def get_availabilities_mulitiple_motives(
        self,
        steps: List[AppointmentStepSearch],
        day: date,
        min_wait_between_steps: Optional[int] = 0,
        max_wait_between_steps: Optional[int] = 30,
    ):
        availabilities = []
        for index, step in enumerate(steps):
            doctors = [
                find_doctor_by_id_company_data(self.company_data, agenda_id)
                for agenda_id in step.agenda_ids
            ]
            doctors = [doctor for doctor in doctors if doctor]
            motive = find_visit_motive(self.company_data, step.visit_motive_id)
            if not motive:
                raise ValueError(
                    f"Visit motive with ID {step.visit_motive_id} not found."
                )

            step_availabilities = self.get_availabilities(
                doctors=doctors, motive=motive, day=day
            )
            availabilities.append(step_availabilities)

        appts_1 = availabilities[0] if availabilities else []
        appts_2 = availabilities[1] if len(availabilities) > 1 else []

        if not appts_1 or not appts_2:
            return []

        min_wait_between_steps = min_wait_between_steps or 0
        max_wait_between_steps = max_wait_between_steps or 30

        steps_combinations = find_consecutive_combinations(
            appts_1, appts_2, min_wait_between_steps, max_wait_between_steps
        )

        valid_combinations = [
            CombinedAppointments(
                start_date=steps[0].start_date,
                end_date=steps[1].end_date,
                steps=steps,
                booking_provider=self.type,
                num_motives=len(steps),
            )
            for steps in steps_combinations
        ]

        return valid_combinations

    def __get_appointment(self, appointment: str):
        endpoint = "/GetStatutsExamens"
        resp = self.post(endpoint, json={"RdvIds": appointment})
        if not resp.get("success"):
            raise Exception(
                f"Error fetching appointment status: {resp.get('errorMessages', 'Unknown error')}"
            )

        examens = resp.get("data", [])

        if len(examens) == 0 or len(examens) > 1:
            raise ValueError(
                f"Expected one appointment, got {len(examens)} for ID {appointment}."
            )

        external_agenda_id = examens[0].get("codeRadiologue")
        doctor = find_doctor_by_id_company_data(self.company_data, external_agenda_id)

        if doctor:
            examens[0]["agenda_id"] = doctor.id
            examens[0]["external_agenda_id"] = doctor.external_id
            examens[0]["equipment_agenda_id"] = examens[0].get("numeroPoste", "")

        return examens[0]

    def get_appointment(self, appointment_id: str) -> AppointmentForm:
        """Fetches an appointment by ID."""
        appointment_raw = self.__get_appointment(appointment_id)
        datePrevue = appointment_raw.get("datePrevue")
        if not datePrevue:
            raise ValueError(
                f"Date prévue not found for appointment ID {appointment_id}."
            )
        appointment_id = appointment_raw.get("rdvId")
        datePrevue = datetime.strptime(datePrevue, "%Y-%m-%dT%H:%M:%S").date()
        heurePrevue = appointment_raw.get("heurePrevue")
        start_date = (
            datetime.strptime(f"{datePrevue} {heurePrevue}", "%Y-%m-%d %H:%M")
            if heurePrevue
            else None
        )
        codeRadiologue = appointment_raw.get("codeRadiologue")

        if not start_date:
            raise ValueError(
                f"Start date not found for appointment ID {appointment_id}."
            )

        doctor = find_doctor_by_id_company_data(self.company_data, codeRadiologue)

        if not doctor:
            raise ValueError(f"Doctor with ID {codeRadiologue} not found.")

        appointment_by_day = self.get_appointments_of_day(
            agenda_ids=[doctor.id],
            day=datePrevue if datePrevue else datetime.now().date(),
        )

        appointment = next(
            (appt for appt in appointment_by_day if appt.id == appointment_id), None
        )

        if not appointment:
            # TODO cannot get all the informations
            return AppointmentForm(
                **appointment_raw,
                start_date=start_date,
                booking_provider="EDL",
                patient_id="EDL_HIDING",
                visit_motive_id=0,  # Use 0 as default visit_motive_id
                speciality_id=0,  # Use 0 as default speciality_id
            )

        return appointment

    def __create_appointment(self, new_appointment: NewAppointment):
        """Creates a new appointment."""
        endpoint = "/SaveRendezVous"
        if not self.company_data.inbound_config_file:
            raise ValueError("Inbound configuration file is missing in company data.")

        patient = None
        if new_appointment.patient_id:
            patient_id = new_appointment.patient_id
            patient = self.search_patient(patient_id)
            if not patient:
                raise ValueError(f"Patient with ID {patient_id} not found.")

            if len(patient) > 1:
                raise ValueError(
                    f"Multiple patients found with ID {patient_id}. Please refine your search."
                )
            patient = patient[0]

        elif (
            new_appointment.first_name
            and new_appointment.last_name
            and new_appointment.birthdate
            and new_appointment.phone_number
        ):
            patient = NewPatient(
                first_name=new_appointment.first_name,
                last_name=new_appointment.last_name,
                birthdate=new_appointment.birthdate,
                phone_number=new_appointment.phone_number,
            )
        else:
            raise ValueError(
                "Patient information must be provided either by patient_id or by first_name, last_name, and birthdate."
            )

        if not new_appointment.equipment_agenda_id:
            raise ValueError(
                "equipment_agenda_id / Numero poste must be provided for the appointment."
            )

        if not new_appointment.visit_motive_id or not new_appointment.agenda_id:
            raise ValueError(
                "visit_motive_id and agenda_id must be provided for the appointment."
            )

        motive = find_visit_motive(self.company_data, new_appointment.visit_motive_id)
        if not motive:
            raise ValueError(
                f"Motive with ID {new_appointment.visit_motive_id} not found."
            )

        doctor = find_doctor_by_id_company_data(
            self.company_data, new_appointment.agenda_id
        )

        if not doctor:
            raise ValueError(f"Doctor with ID {new_appointment.agenda_id} not found.")

        body = {
            "ExternalUserNumber": "VOCCA",
            "NumeroDossier": (patient.id if isinstance(patient, Patient) else None),
            "NomPatient": patient.last_name,
            "PrenomPatient": patient.first_name,
            "DateNaissance": (
                patient.birthdate.strftime("%Y-%m-%d") if patient.birthdate else ""
            ),
            "TelPortable": patient.phone_number,
            "TypeExamen": motive.speciality_external_id,
            "CodeExamen": motive.external_id,
            "CodeMedecin": doctor.external_id,
            "NumeroPoste": new_appointment.equipment_agenda_id,
            "CodeSite": self.company_data.inbound_config_file.practice_id,
            "Date": new_appointment.start_date.strftime("%Y-%m-%d"),
            "HeureDebut": new_appointment.start_date.strftime("%H:%M"),
            "UserIP": self.session.headers.get("X-Forwarded-For", "<unknown>"),
            "Commentaires": new_appointment.notes,
        }

        resp = self.post(endpoint, json=body)

        if not resp.get("success"):
            raise Exception(
                f"Error creating appointment: {resp.get('errorMessages', 'Unknown error')}"
            )

        data = resp.get("data", {})

        if not motive.speciality_id:
            raise ValueError(f"Speciality ID not found for motive {motive.name}.")

        return AppointmentForm(
            id=data.get("numeroExamen"),
            patient_id=data.get("numeroDossier"),
            visit_motive_id=new_appointment.visit_motive_id,
            visit_motive_name=motive.name,
            practitioner_id=doctor.practitioner_id,
            external_agenda_id=doctor.external_id,
            start_date=new_appointment.start_date,
            agenda_id=new_appointment.agenda_id,
            end_date=new_appointment.end_date,
            speciality_id=motive.speciality_id,
            equipment_agenda_id=new_appointment.equipment_agenda_id,
            status="confirmed",
        )

    def create_appointment(self, new_appointment: NewAppointment) -> AppointmentForm:
        if not new_appointment.steps:
            return self.__create_appointment(new_appointment)
        elif new_appointment.steps and len(new_appointment.steps) == 1:
            new_appointment.start_date = new_appointment.steps[0].start_date
            new_appointment.agenda_id = new_appointment.steps[0].agenda_id
            new_appointment.visit_motive_id = new_appointment.steps[0].visit_motive_id
            new_appointment.equipment_agenda_id = new_appointment.steps[
                0
            ].equipment_agenda_id
            new_appointment.notes = new_appointment.steps[0].notes

            return self.__create_appointment(new_appointment)
        else:
            step_appointments: List[AppointmentForm] = []
            for step in new_appointment.steps:
                step.patient_id = new_appointment.patient_id
                step_new_appt = self.__create_appointment(
                    NewAppointment(
                        **step.model_dump(),
                        first_name=new_appointment.first_name,
                        last_name=new_appointment.last_name,
                        birthdate=new_appointment.birthdate,
                    ),
                )
                step_appointments.append(step_new_appt)
            if len(step_appointments) == 0:
                raise ValueError("No valid appointment steps were created.")

            steps = [
                AppointmentFormStep(**sub_appt.model_dump())
                for sub_appt in step_appointments
            ]
            return AppointmentForm(
                id=step_appointments[0].id,
                agenda_id=step_appointments[0].agenda_id,
                visit_motive_id=step_appointments[0].visit_motive_id,
                visit_motive_name=step_appointments[0].visit_motive_name,
                steps=steps,
                patient_id=step_appointments[0].patient_id,
                start_date=step_appointments[0].start_date,
                external_agenda_id=step_appointments[0].external_agenda_id,
                status="confirmed",
            )

    def update_appointment(self, appointment_id, data: dict) -> AppointmentForm:
        """Updates an existing appointment."""
        endpoint = "/AddCommentaires"

        if not appointment_id:
            raise ValueError("Appointment ID must be provided.")

        if not data:
            raise ValueError("Data must be provided for updating the appointment.")

        for item in data.keys():
            if item not in ["notes", "id", "config"]:
                logger.warning(
                    f"EDL: Unexpected field  '{item}' found in appointment update data."
                )
                raise ValueError(
                    f"Unexpected field '{item}' found in appointment update data for EDL."
                )

        notes = data.get("notes", "")

        body = {
            "idExamen": appointment_id,
            "ExternalUserNumber": "VOCCA",
            "Commentaires": notes,
        }

        resp = self.post(endpoint, json=body)

        if not resp.get("success"):
            raise Exception(
                f"Error updating appointment: {resp.get('errorMessages', 'Unknown error')}"
            )

        appointment = self.get_appointment(appointment_id)
        # EDL does not send back the "commentaires", we just put them back in the form.
        if notes:
            appointment.notes = notes

        return appointment

    def confirm_appointment(self, appointment_id: str, notes: str = ""):
        """Confirms an existing appointment."""
        endpoint = "/ConfirmRendezVous"

        if not appointment_id:
            raise ValueError("Appointment ID must be provided.")

        appointment = self.get_appointment(appointment_id)
        patient = self.get_patient(appointment.patient_id)

        ip_address = self.session.headers.get("X-Forwarded-For", "<unknown>")
        if not patient.birthdate:
            raise ValueError("Patient birthdate must be provided for confirmation.")

        body = {
            "rdvId": appointment_id,
            "ExternalUserNumber": "VOCCA",
            "Commentaires": notes,
            "NomPatient": patient.last_name,
            "PrenomPatient": patient.first_name,
            "DateNaissance": patient.birthdate.strftime("%Y-%m-%d"),
            "UserIP": ip_address,
        }

        resp = self.post(endpoint, json=body)

        if not resp.get("success"):
            raise Exception(
                f"Error confirming appointment: {resp.get('errorMessages', 'Unknown error')}"
            )

        appt = self.get_appointment(appointment_id)
        return appt

    def cancel_appointment(self, appointment_id: str, notes: str = ""):
        """Cancels an existing appointment."""
        endpoint = "/DeleteRendezVous"
        if not appointment_id:
            raise ValueError("Appointment ID must be provided.")

        if notes:
            logger.warning("Notes are not used in the cancellation process for EDL.")

        appointment = self.get_appointment(appointment_id)

        if "EDL_HIDING" == appointment.patient_id:
            raise ValueError(
                "Name of the patient is hidden, the appointment is probably already cancelled."
            )

        patient = self.get_patient(appointment.patient_id)

        if not patient.birthdate:
            raise ValueError("Patient birthdate must be provided for cancellation.")

        body = {
            "RdvId": appointment_id,
            "ExternalUserNumber": appointment.patient_id,
            "NomPatient": patient.last_name,
            "PrenomPatient": patient.first_name,
            "DateNaissance": patient.birthdate.strftime("%Y-%m-%d"),
            "UserIP": self.session.headers.get("X-Forwarded-For", "<unknown>"),
        }

        resp = self.post(endpoint, json=body)

        if not resp.get("success"):
            raise Exception(
                f"Error canceling appointment {appointment_id}: {resp.get('errorMessages', 'Unknown error')}"
            )

        appointment.status = "canceled"
        return appointment

    def get_appointments_of_day(
        self, agenda_ids: List[int], day: date
    ) -> List[AppointmentForm]:
        """Fetches appointments for a specific day and agenda."""
        if not agenda_ids:
            raise ValueError("At least one agenda ID must be provided.")

        if not day:
            raise ValueError("Day must be provided.")

        endpoint = "/GetAppointmentsByDay"

        appointments = []
        for agenda_id in agenda_ids:
            if not agenda_id:
                raise ValueError("All agenda IDs must be valid.")

            doctor = find_doctor_by_id_company_data(self.company_data, agenda_id)

            if not doctor:
                raise ValueError(
                    f"Doctor with ID {agenda_id} not found in company data."
                )

            body = {
                "CodeRadiologue": doctor.external_id,
                "DateDebut": day.strftime("%Y-%m-%d") + "T00:00:00",
            }

            resp = self.post(endpoint, json=body)
            if not resp.get("success"):
                raise Exception(
                    f"Error fetching appointments for agenda {agenda_id}: {resp.get('errorMessages', 'Unknown error')}"
                )

            data = resp.get("data", [])
            for raw_appointment in data:
                start_time = raw_appointment.get("heure", "00:00")
                start_datetime = datetime.strptime(
                    f"{day.strftime('%Y-%m-%d')}T{start_time}", "%Y-%m-%dT%H:%M"
                )

                if not start_datetime.tzinfo:
                    start_datetime = start_datetime.replace(
                        tzinfo=ZoneInfo("Europe/Paris")
                    )

                patients = self.search_patient(raw_appointment.get("numeroDossier"))
                if len(patients) == 0 or len(patients) > 1:
                    raise ValueError(
                        f"Invalid patient data for appointment {raw_appointment.get('numeroDossier')}"
                    )

                patient = patients[0]

                appointment = next(
                    (
                        appt
                        for appt in patient.appointments
                        if appt.start_date == start_datetime
                    ),
                    None,
                )
                if not appointment:
                    logger.warning(
                        "No appointments found for patient %s at %s",
                        patient.id,
                        start_datetime,
                    )
                    continue

                appointments.append(appointment)

        return appointments
