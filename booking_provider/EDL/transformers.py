from datetime import datetime


def edl_transformer_appointment(cls, values):
    if "codesMedecins" in values:
        values["external_agenda_id"] = (
            str(values.get("codesMedecins")[0])
            if len(values.get("codesMedecins", [])) > 0
            else None
        )

    if "numeroPoste" in values:
        values["equipment_agenda_id"] = str(values.get("numeroPoste"))

    date = values.get("date")
    heure_debut = values.get("heureDebut")
    heure_fin = values.get("heureFin")
    if date and heure_debut and heure_fin:
        date = datetime.strptime(values.get("date"), "%Y-%m-%dT%H:%M:%S").date()
        values["start_date"] = datetime.strptime(
            f"{date}T{heure_debut}", "%Y-%m-%dT%H:%M"
        )
        values["end_date"] = datetime.strptime(f"{date}T{heure_fin}", "%Y-%m-%dT%H:%M")

    if "codeSite" in values:
        values["practice_id"] = str(values.get("codeSite"))

    if "statut" in values:
        if values.get("statut") == "S":
            values["status"] = "deleted"
        else:
            values["status"] = "confirmed"

    return values


def edl_transformer_appointment_form(cls, values):
    if "numeroExamen" in values:
        values["id"] = str(values.get("numeroExamen"))
    if "rdvId" in values:
        values["id"] = str(values.get("rdvId"))

    if "idPatient" in values:
        values["patient_id"] = str(values.get("idPatient"))
    if "numeroDossier" in values:
        values["patient_id"] = str(values.get("numeroDossier"))
    if "idExamen" in values:
        values["id"] = str(values.get("idExamen"))
    if "codeExamen" in values:
        # transform char to int
        values["visit_motive_id"] = int(
            "".join([str(ord(c)) for c in values.get("codeExamen", "")])
        )
    if "datePrevue" in values and "heurePrevue" in values:
        # 2025-01-01T00:00:00
        start_date = values.get("datePrevue")
        start_date = datetime.strptime(start_date, "%Y-%m-%dT%H:%M:%S")
        start_time = values.get("heurePrevue")
        values["start_date"] = datetime.strptime(
            f"{start_date.date()}T{start_time}", "%Y-%m-%dT%H:%M"
        )

    if "numeroPoste" in values:
        values["equipment_agenda_id"] = str(values.get("numeroPoste"))

    if "datePrevue" in values and "heurePrevue" in values:
        # 2025-01-01T00:00:00
        start_date = values.get("datePrevue")
        start_date = datetime.strptime(start_date, "%Y-%m-%dT%H:%M:%S")
        start_time = values.get("heurePrevue")
        values["start_date"] = datetime.strptime(
            f"{start_date.date()}T{start_time}", "%Y-%m-%dT%H:%M"
        )

    if "codeRadiologue" in values:
        values["external_agenda_id"] = str(values.get("codeRadiologue"))

    if "numeroPoste" in values:
        values["equipment_agenda_id"] = str(values.get("numeroPoste"))

    if "dateNaissance" in values:
        values["birthdate"] = datetime.strptime(values.get("dateNaissance"), "%Y-%m-%d")

    if "statut" in values:
        if values.get("statut") == "S":
            values["status"] = "deleted"
        else:
            values["status"] = "confirmed"

    return values
