from typing import List, Dict

from loguru import logger
from supabase import Client, create_client

from constants import SUPABASE_KEY, SUPABASE_URL
from models import Booking, CompanyData, ConfirmationParams, Doctor

# Supabase tables
KNOWLEDGE_BASE_TABLE = "knowledge-bases"
OTP_DOCTOLIB_TABLE = "otp_doctolib"
CALL_TABLE = "calls_booking_task"
NO_SHOW_RULES_TABLE = "no_show_appointment_rules"
RECALLS_POSTE_PROCESSING_CONFIG = "recalls_post_processing_config"  # table that lets you know what keys to use to confirm an appointment


if not SUPABASE_URL or not SUPABASE_KEY:
    raise ValueError("Supabase URL or API Key is missing in .env")


class SupabaseClient:
    def __init__(self, url: str, key: str):
        """Initialize the Supabase client."""
        self.db: Client = create_client(url, key)

    def get_supabase(self) -> Client:
        """Returns the shared Supabase client instance."""
        return self.db

    def get_all_company_data(self) -> list[CompanyData]:
        """Returns all configs from the 'knowledge-bases' table."""
        response = (
            self.db.table(KNOWLEDGE_BASE_TABLE)
            .select("*")
            .eq("is_active", "TRUE")
            .execute()
        )
        company_data_list = []
        for company_data in response.data:
            company_data_list.append(CompanyData(**company_data))
        return company_data_list

    def get_company_data_by_config_id(self, config_id: str) -> CompanyData:
        """Fetches company data from the 'knowledge-bases' table using config_id."""
        response = (
            self.db.table(KNOWLEDGE_BASE_TABLE)
            .select("*")
            .eq("config", config_id)
            .execute()
        )
        company_data_dict = response.data[0] if response.data else None
        if company_data_dict:
            return CompanyData(**company_data_dict)
        else:
            raise ValueError(f"Company data not found for config_id: {config_id}")

    def get_agenda_by_id(self, config_id: str, agenda_id: int) -> Doctor:
        agenda_id_str = str(agenda_id)

        company_data = self.get_company_data_by_config_id(config_id)
        if not company_data or not company_data.inbound_config_file:
            raise ValueError(
                f"Company data not found or invalid for config_id: {config_id}"
            )

        doctors = company_data.inbound_config_file.calendars
        for doctor in doctors:
            if str(doctor.id) == agenda_id_str:
                return doctor

        raise ValueError(
            f"Doctor with ID {agenda_id} not found for config ID: {config_id}"
        )

    def get_booking(self, id: str) -> Booking:
        """Fetches appointment details by vocca appointment ID."""
        response = self.db.table("bookings").select("*").eq("id", id).execute()
        if not response.data:
            raise ValueError(f"Booking with ID {id} not found.")

        return Booking(**response.data[0])

    def get_appointment_id_from_booking_id(self, id: str) -> str:
        """Fetches appointment ID by vocca appointment ID."""
        try:
            booking = self.get_booking(id)
        except ValueError as e:
            raise ValueError(f"Error fetching appointment: {e}")

        appointment_id = booking.appointment_id
        if not appointment_id:
            raise ValueError("Appointment ID is missing in the fetched data.")

        if "doctolib" in appointment_id:
            return appointment_id.split("/")[-1]

        return appointment_id

    def get_no_show_rule(self, config: str):
        company_data = self.get_company_data_by_config_id(config)

        no_show_rule = (
            self.db.table(NO_SHOW_RULES_TABLE)
            .select("*")
            .eq("id", company_data.no_show_rule_id)
            .execute()
        )

        if no_show_rule.data:
            logger.info(
                f"Found no show rule for config: {config}, rule: {no_show_rule.data[0]}"
            )
            return no_show_rule.data[0]

        logger.warning(
            f"No show rule not found for config: {config}, using default rule."
        )

        return None

    def get_companies_dict(self) -> List[Dict]:
        resp = self.db.table(KNOWLEDGE_BASE_TABLE).select("*").execute()
        return resp.data if resp.data else []

    def get_confirmation_params(self, config_id: str) -> ConfirmationParams:
        resp = (
            self.db.table(RECALLS_POSTE_PROCESSING_CONFIG)
            .select(
                "confirmation_note,confirmation_field_key,confirmation_field_value,confirmation_type"
            )
            .eq("config", config_id)
            .execute()
        )
        confirmation_params = resp.data[0] if resp.data else None
        if confirmation_params:
            return ConfirmationParams(
                confirmation_note=confirmation_params.get(
                    "confirmation_note", "Rendez-vous confirmé par Vocca"
                ),
                confirmation_field_key=confirmation_params.get(
                    "confirmation_field_key", ""
                ),
                confirmation_field_value=confirmation_params.get(
                    "confirmation_field_value", ""
                ),
                confirmation_type=confirmation_params.get("confirmation_type", "note"),
            )

        return ConfirmationParams(
            confirmation_note="Rendez-vous confirmé par Vocca",
            confirmation_field_key="",
            confirmation_field_value="",
            confirmation_type="note",
        )


supabase_client = SupabaseClient(SUPABASE_URL, SUPABASE_KEY)
