from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from loguru import logger

from constants import DEBUG, LOG_LEVEL, PORT
from routers import (agendas, appointments, bookings, configurator, health,
                     motives, patients, specialities)

app = FastAPI(title="EDL API")

# Configuration CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


try:

    @app.get("/")
    async def root():
        return {"status": "healthy", "message": "EDL API is running"}

    @app.get("/healthz")
    async def health_check():
        return {"status": "healthy"}

    version = "/api/v1"
    app.include_router(health.router, prefix=f"{version}/health", tags=["health"])
    app.include_router(
        configurator.router, prefix=f"{version}/configurator", tags=["configurator"]
    )

    app.include_router(patients.router, prefix=f"{version}/patients", tags=["patients"])
    app.include_router(
        specialities.router, prefix=f"{version}/specialities", tags=["specialities"]
    )
    app.include_router(
        appointments.router, prefix=f"{version}/appointments", tags=["appointments"]
    )
    app.include_router(agendas.router, prefix=f"{version}/agendas", tags=["agendas"])
    app.include_router(motives.router, prefix=f"{version}/motives", tags=["motives"])
    app.include_router(bookings.router, prefix=f"{version}/bookings", tags=["bookings"])
except Exception as e:
    logger.error(f"Error occurred while including routers: {str(e)}")


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "main:app", host="0.0.0.0", port=PORT, reload=DEBUG, log_level=LOG_LEVEL
    )
