import os

from dotenv import load_dotenv

load_dotenv()

PORT = int(os.getenv("PORT", 8000))  # Default to 8000 if not set
DEBUG = os.getenv("DEBUG", "False").lower() in ("true", "1", "yes")
LOG_LEVEL = os.getenv("LOG_LEVEL", "info").lower()
SUPABASE_URL = os.getenv("SUPABASE_URL", "")
SUPABASE_KEY = os.getenv("SUPABASE_KEY", "")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", "")
DOCTOLIB_SECRET_SCRAP_URL = os.getenv("DOCTOLIB_SECRET_SCRAP_URL", "")
DOCTOLIB_SECRET_SCRAP_TOKEN = os.getenv("DOCTOLIB_SECRET_SCRAP_TOKEN", "")

required_env_vars = [
    "SUPABASE_URL",
    "SUPABASE_KEY",
    "OPENAI_API_KEY",
    "JWT_SECRET_KEY",
    "DOCTOLIB_SECRET_SCRAP_URL",
    "DOCTOLIB_SECRET_SCRAP_TOKEN",
]

for var in required_env_vars:
    if not os.getenv(var):
        raise ValueError(f"Missing required environment variable: {var}")
