from typing import Optional

from pydantic import BaseModel, model_validator


class Speciality(BaseModel):
    id: int
    name: str
    external_id: Optional[str] = None
    alias_id: Optional[int] = None
    late_arrival_limit: Optional[int] = None
    should_transfer: Optional[bool] = False

    @model_validator(mode="before")
    def validate_id(cls, values):
        if values.get("id") and not isinstance(values["id"], int):
            values["id"] = int(values["id"])

        if not values.get("external_id"):
            values["external_id"] = str(values.get("id"))

        if values.get("libelle"):
            values["name"] = values.get("libelle")

        if values.get("code"):
            values["external_id"] = values.get("code")

        return values
