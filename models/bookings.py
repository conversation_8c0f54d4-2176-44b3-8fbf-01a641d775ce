from datetime import datetime

from dateutil.parser import parse
from pydantic import BaseModel, model_validator


class Booking(BaseModel):
    id: str
    config_id: str
    phone_number: str
    full_name: str
    appointment_id: str
    appointment_start_date: datetime
    is_new_patient: bool

    @model_validator(mode="before")
    def transform(cls, values):
        if "config" in values:
            values["config_id"] = str(values["config"])

        if isinstance(values.get("appointment_start_date"), str):
            values["appointment_start_date"] = parse(values["appointment_start_date"])

        if not values.get("is_new_patient"):
            # V1 inbound bot does not have is_new_patient
            values["is_new_patient"] = False

        return values


class JWTBookingPayload(BaseModel):
    booking_id: str
    appointment_id: str
    config_id: str
