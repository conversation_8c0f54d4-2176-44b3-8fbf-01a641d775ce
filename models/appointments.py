from datetime import date, datetime
from typing import List, Literal, Optional
from zoneinfo import ZoneInfo

from dateutil.parser import parse
from pydantic import BaseModel, Field, field_serializer, model_validator

from booking_provider.DOCTOLIB_SECRET_SCRAP.transformers import (
    doctolib_transformer_appointment, doctolib_transformer_appointment_form)
from booking_provider.EDL.transformers import (
    edl_transformer_appointment, edl_transformer_appointment_form)

from .doctors import Doctor
from .motives import Motive


class Appointment(BaseModel):
    start_date: datetime
    end_date: datetime
    visit_motive_id: int
    agenda_id: Optional[int] = None
    external_agenda_id: Optional[str] = None
    medecin: Optional[str] = None
    practitioner_agenda_id: Optional[str] = None
    practice_id: Optional[str] = None
    equipment_agenda_id: Optional[str] = None
    booking_provider: Optional[str] = None

    @model_validator(mode="before")
    def transform(cls, values):
        if isinstance(values.get("agenda_id"), str):
            values["agenda_id"] = int(values.get("agenda_id"))

        if values.get("booking_provider") == "EDL":
            values = edl_transformer_appointment(cls, values)

        if values.get("booking_provider") == "DOCTOLIB":
            values = doctolib_transformer_appointment(cls, values)

        return values


class CombinedAppointments(BaseModel):
    start_date: datetime
    end_date: datetime
    steps: List[Appointment]
    booking_provider: str
    num_motives: int


class AppointmentFormStep(BaseModel):
    visit_motive_id: int
    start_date: datetime
    agenda_id: int
    external_agenda_id: Optional[str] = None
    id: Optional[str] = None
    patient_id: Optional[str] = None
    notes: Optional[str] = None
    end_date: Optional[datetime] = None
    practitioner_id: Optional[str] = None
    speciality_id: Optional[int] = None
    visit_motive_name: Optional[str] = None
    instructions: Optional[str] = None
    equipment_agenda_id: Optional[str] = None
    status: Optional[str] = None

    @field_serializer("start_date", "end_date")
    def serialize_start_date(self, value: datetime) -> str:
        return value.strftime("%Y-%m-%dT%H:%M:%S")


class AppointmentForm(BaseModel):
    patient_id: str
    visit_motive_id: int
    start_date: datetime
    agenda_id: Optional[int] = None
    external_agenda_id: str
    status: str
    id: Optional[str]
    notes: Optional[str] = None
    practitioner_id: Optional[str] = None
    speciality_id: Optional[int] = None
    end_date: Optional[datetime] = None
    visit_motive_name: Optional[str] = None
    instructions: Optional[str] = None
    equipment_agenda_id: Optional[str] = None
    booking_provider: Optional[str] = None
    custom_fields_values: Optional[dict] = Field(default_factory=dict)
    steps: Optional[List[AppointmentFormStep]] = Field(default_factory=list)

    @model_validator(mode="before")
    def transform(cls, values):
        if isinstance(values.get("visit_motive_id"), str):
            values["visit_motive_id"] = int(values.get("visit_motive_id"))

        if isinstance(values.get("agenda_id"), str):
            values["agenda_id"] = int(values.get("agenda_id"))

        if values.get("booking_provider") == "EDL":
            values = edl_transformer_appointment_form(cls, values)

        if values.get("booking_provider") == "DOCTOLIB":
            values = doctolib_transformer_appointment_form(cls, values)

        if values.get("start_date") and isinstance(values.get("start_date"), str):
            values["start_date"] = parse(values["start_date"])

        if values.get("start_date").tzinfo is None:
            values["start_date"] = values["start_date"].replace(
                tzinfo=ZoneInfo("Europe/Paris")
            )

        if not values.get("external_agenda_id"):
            values["external_agenda_id"] = str(values.get("agenda_id"))

        if values.get("steps") and isinstance(values.get("steps"), list):
            tmp_step = []
            for step in values["steps"]:
                if isinstance(step, AppointmentFormStep):
                    tmp_step.append(step)
                else:
                    step = AppointmentFormStep(**step)
                    tmp_step.append(step)

            values["steps"] = tmp_step

        return values


class NewAppointment(BaseModel):
    start_date: datetime
    visit_motive_id: Optional[int]
    agenda_id: Optional[int]
    end_date: Optional[datetime] = None
    patient_id: Optional[str] = None
    phone_number: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    birthdate: Optional[datetime] = None
    equipment_agenda_id: Optional[str] = None
    notes: Optional[str] = None
    steps: List[AppointmentFormStep] = Field(default_factory=list)

    @field_serializer("start_date", "end_date")
    def serialize_start_date(self, value: datetime) -> str:
        return value.strftime("%Y-%m-%dT%H:%M:%S")

    @model_validator(mode="before")
    def validate_steps(cls, values):
        if values.get("steps") and isinstance(values.get("steps"), list):
            values["steps"] = [AppointmentFormStep(**step) for step in values["steps"]]

        return values


class ConfirmationParams(BaseModel):
    confirmation_note: Optional[str] = Field(
        default="", description="Note for the confirmation"
    )
    confirmation_field_key: Optional[str] = Field(
        ..., description="Key for the custom field"
    )
    confirmation_field_value: Optional[str] = Field(
        ..., description="Value for the custom field"
    )
    confirmation_type: Literal["note", "custom_field"] = Field(default="note")


class AppointmentSearch(BaseModel):
    day: date
    motive: Motive
    doctors: List[Doctor]


class AppointmentStepSearch(BaseModel):
    agenda_ids: List[int]
    visit_motive_id: int
