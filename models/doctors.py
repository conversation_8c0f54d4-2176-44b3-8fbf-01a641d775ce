from typing import Optional

from pydantic import BaseModel, model_validator


class Doctor(BaseModel):
    id: int
    name: str
    external_id: str
    practitioner_id: Optional[str] = None
    speciality_id: Optional[int] = None
    age_minimum: Optional[int] = None
    age_maximum: Optional[int] = None
    information: Optional[str] = None

    @model_validator(mode="before")
    def transform_keys(cls, values):
        if "id" in values and isinstance(values.get("id"), int):
            values["id"] = str(values["id"])

        if "speciality_id" in values and isinstance(values.get("speciality_id"), str):
            values["speciality_id"] = int(values["speciality_id"])

        if "practitioner_id" in values and isinstance(
            values.get("practitioner_id"), int
        ):
            values["practitioner_id"] = str(values["practitioner_id"])

        if not values.get("external_id"):
            values["external_id"] = str(values.get("id"))

        if "nom" in values:
            values["name"] = values.pop("nom") + " " + values.pop("prenom", "")

        if "numeroRPPS" in values:
            values["practitioner_id"] = str(values.pop("numeroRPPS"))

        return values


class SubstituteDoctor(BaseModel):
    id: int
    name: str
