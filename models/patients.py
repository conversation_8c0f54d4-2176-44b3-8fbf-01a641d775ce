from datetime import date, datetime
from typing import Dict, List, Optional

from dateutil.parser import parse
from pydantic import BaseModel, field_serializer, model_validator

from models import AppointmentForm, Doctor


class NewPatient(BaseModel):
    first_name: str
    last_name: str
    phone_number: str
    birthdate: datetime
    gender: Optional[bool] = None

    @field_serializer("birthdate")
    def serialize_birthdate(self, value: datetime) -> str | None:
        """Serialize birthdate to string format."""
        return value.strftime("%Y-%m-%d") if value else None


class Patient(BaseModel):
    is_new_patient: bool
    id: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    birthdate: Optional[date] = None
    email: Optional[str] = ""
    phone_number: Optional[str] = None
    address: Optional[str] = ""
    city: Optional[str] = None
    zipcode: Optional[str] = None
    bounced_at: Optional[str] = None
    """If there is a datetime value, it means that the patient is not allowed to book an appointment"""
    next_appointment: Optional[AppointmentForm] = None
    last_appointment: Optional[AppointmentForm] = None
    appointments: List[AppointmentForm] = []
    medecin_historique: Optional[Doctor] = None
    historical_doctors: Optional[Dict[int, Doctor]] = {}
    """Historical Doctor per speciality_id """
    num_no_show: Optional[int] = 0
    gender: Optional[bool] = None

    @model_validator(mode="before")
    def transform(cls, values):
        if "sexe" in values:
            values["gender"] = str(values.get("sexe"))

        if "numeroDossier" in values:
            values["id"] = str(values.get("numeroDossier"))
            values["is_new_patient"] = False if values.get("numeroDossier") else True

        if "nom" in values:
            values["last_name"] = str(values.get("nom"))

        if "prenom" in values:
            values["first_name"] = str(values.get("prenom"))

        if "dateNaissance" in values:
            values["birthdate"] = parse(values.get("dateNaissance")).date()

        if "telephonePortable" in values:
            values["phone_number"] = str(values.get("telephonePortable"))

        return values

    @field_serializer("birthdate")
    def serialize_birthdate(self, value: datetime) -> str | None:
        """Serialize birthdate to string format."""
        return value.strftime("%Y-%m-%d") if value else None


class UpdatePatient(BaseModel):
    id: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    phone_number: Optional[str] = None
    birthdate: Optional[datetime] = None
    gender: Optional[bool] = None
    zipcode: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None

    @field_serializer("birthdate")
    def serialize_birthdate(self, value: datetime) -> str | None:
        """Serialize birthdate to string format."""
        return value.strftime("%Y-%m-%d") if value else None

    @model_validator(mode="before")
    def transform(cls, values):
        if "gender" in values and values.get("gender") in ["male", "female"]:
            values["gender"] = values.get("gender") == "female"

        return values
