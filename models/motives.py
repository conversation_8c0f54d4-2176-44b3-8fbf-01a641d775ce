from typing import List, Optional

from pydantic import BaseModel, model_validator


class Motive(BaseModel):
    name: str
    id: int
    speciality_id: Optional[int] = None
    external_id: Optional[str] = None
    medecin: Optional[str] = None
    after: Optional[str] = None
    before: Optional[str] = None
    instructions: Optional[str] = None
    outbound_instructions: Optional[str] = None
    open: Optional[bool] = False  # Use a proper default type
    price: Optional[str] = None
    type: Optional[str] = None
    age_minimum: Optional[float] = None
    age_maximum: Optional[float] = None
    speciality_external_id: Optional[str] = None
    metadata: Optional[List[str]] = []
    days_before_max: Optional[int] = None
    days_before_min: Optional[int] = None
    should_transfer: Optional[bool] = False

    @model_validator(mode="before")
    def transform_keys(cls, values):
        if "id" in values and isinstance(values["id"], int):
            values["id"] = str(values["id"])

        if not values.get("external_id"):
            values["external_id"] = str(values.get("id"))

        if "speciality_id" in values and isinstance(values["speciality_id"], str):
            values["speciality_id"] = int(values["speciality_id"])

        if "libelle" in values:
            values["name"] = values.get("libelle")

        if "libelleXplore" in values:
            libelle = values.get("libelleXplore")
            speciality_code = libelle.split(" ")[0]
            values["speciality_external_id"] = speciality_code

        if "code" in values:
            values["external_id"] = str(values.get("code"))

        return values
