from datetime import datetime
from typing import List, Optional

from loguru import logger
from pydantic import (BaseModel, Field, field_serializer, field_validator,
                      model_validator)

from models import Doctor, Motive, Speciality


class InboundConfig(BaseModel):
    organization_id: str
    patient_base_id: str
    practice_id: str
    calendars: List[Doctor]
    specialities: List[Speciality]
    visit_motives_categories: List[Motive] = Field(
        default_factory=list, serialization_alias="visit-motive-categories"
    )

    @field_validator("calendars", mode="before")
    @classmethod
    def validate_calendars_is_list(cls, v):
        if v is not None and not isinstance(v, list):
            raise ValueError(
                f"'calendars' field must be a list, got {type(v).__name__}: {v}"
            )
        return v

    @field_validator("specialities", mode="before")
    @classmethod
    def validate_specialities_is_list(cls, v):
        if v is not None and not isinstance(v, list):
            raise ValueError(
                f"'specialities' field must be a list, got {type(v).__name__}: {v}"
            )
        return v

    @field_validator("visit_motives_categories", mode="before")
    @classmethod
    def validate_visit_motives_categories_is_list(cls, v):
        if v is not None and not isinstance(v, list):
            raise ValueError(
                f"'visit_motives_categories' field must be a list, got {type(v).__name__}: {v}"
            )
        return v

    @model_validator(mode="before")
    def transform_keys(cls, values):
        if isinstance(values.get("organization_id"), int):
            values["organization_id"] = str(values["organization_id"])

        if isinstance(values.get("patient_base_id"), int):
            values["patient_base_id"] = str(values["patient_base_id"])

        if isinstance(values.get("practice_id"), int):
            values["practice_id"] = str(values["practice_id"])

        if "visit-motives-categories" in values:
            values["visit_motives_categories"] = []
            for motive in values["visit-motives-categories"]:
                if isinstance(motive, dict):
                    values["visit_motives_categories"].append(Motive(**motive))
                elif isinstance(motive, Motive):
                    values["visit_motives_categories"].append(motive)
                else:
                    logger.error(f"Invalid motive type: {type(motive).__name__}")
                    raise ValueError(f"Invalid motive type: {type(motive).__name__}")

        if "calendars" in values:
            values["calendars_tmp"] = []
            for calendar in values["calendars"]:
                if isinstance(calendar, dict):
                    values["calendars_tmp"].append(Doctor(**calendar))
                elif isinstance(calendar, Doctor):
                    values["calendars_tmp"].append(calendar)
                else:
                    logger.error(f"Invalid calendar type: {type(calendar).__name__}")
                    raise ValueError(
                        f"Invalid calendar type: {type(calendar).__name__}"
                    )
            values["calendars"] = values["calendars_tmp"]

        if "specialities" in values:
            values["specialities_tmp"] = []
            for speciality in values["specialities"]:
                if isinstance(speciality, dict):
                    values["specialities_tmp"].append(Speciality(**speciality))
                elif isinstance(speciality, Speciality):
                    values["specialities_tmp"].append(speciality)
                else:
                    logger.error(
                        f"Invalid speciality type: {type(speciality).__name__}"
                    )
                    raise ValueError(
                        f"Invalid speciality type: {type(speciality).__name__}"
                    )

            values["specialities"] = values["specialities_tmp"]

        return values


class CompanyData(BaseModel):
    config: str
    name: str
    address: Optional[str] = None
    email: Optional[str] = None
    additional_information: Optional[str] = None
    created_at: datetime
    openings: Optional[str] = None
    agenda_id: Optional[List[int]] = None
    forward_number: Optional[str] = None
    search_url: Optional[str] = None
    embed_url: Optional[str] = None
    config_name: Optional[str] = None
    booking_url: Optional[str] = None
    inbound_config_file: InboundConfig
    outbound_config_file: Optional[dict] = None
    openings_2: Optional[dict] = None
    client_name: Optional[str] = None
    airtable_booking_url: Optional[str] = None
    task_in_doctolib: Optional[bool] = False
    booking_provider: Optional[str] = None
    twilio_inbound_call_phone_number: Optional[str] = None
    phone_number_center: Optional[str] = None
    calendars_ignored: Optional[List[dict]] = Field(default_factory=list)
    no_show_rule_id: Optional[int] = None

    @field_serializer("created_at")
    def serialize_datetime(self, dt: datetime) -> str:
        return dt.isoformat()
