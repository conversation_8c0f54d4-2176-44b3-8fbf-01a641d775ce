import json
import unicodedata
from typing import List, Optional

from loguru import logger
from openai import OpenAI
from rapidfuzz import fuzz

from constants import OPENAI_API_KEY
from lib.supabase_client import supabase_client
from models import CompanyD<PERSON>, <PERSON>, Motive

from .doctors import find_doctor_by_name
from .json import extract_json_from_string

MIN_SIMILARITY_THRESHOLD = 70  # Minimum similarity score for fuzzy matching


def find_visit_motive(
    company_data: CompanyData, motive_input: str | int, skip_llm_search: bool = False
) -> Optional[Motive]:
    motive_str = str(motive_input).strip()

    if not company_data or not company_data.inbound_config_file:
        raise ValueError("Company data is not available or incomplete.")

    visit_motives = company_data.inbound_config_file.visit_motives_categories

    if motive_str.isdigit():
        if motive := match_by_id(visit_motives, motive_str):
            return motive

    # search with external_id
    if motive := match_by_external_id(visit_motives, motive_str):
        return motive

    visit_motives_no_doctor_name = [
        m for m in visit_motives if not m.medecin or m.medecin == ""
    ]
    if motive := match_by_fuzzy_name(visit_motives_no_doctor_name, motive_str):
        return motive

    if not skip_llm_search:
        if motive := match_using_llm(visit_motives_no_doctor_name, motive_str):
            return motive

    logger.info("No matching motive found")
    return None


def match_by_external_id(motives: List[Motive], external_id: str) -> Optional[Motive]:
    external_id = external_id.lower().strip()
    for motive in motives:
        if not motive.external_id:
            continue

        if motive.external_id and motive.external_id.lower() == external_id.lower():
            return motive

    return None


def match_by_id(motives: List[Motive], motive_id: str | int) -> Optional[Motive]:
    if isinstance(motive_id, int):
        motive_id = str(motive_id)

    return next(
        (
            m
            for m in motives
            if str(m.id) == str(motive_id) or str(m.external_id) == str(motive_id)
        ),
        None,
    )


def match_by_fuzzy_name(motives: List[Motive], input_name: str) -> Optional[Motive]:
    input_name = input_name.lower().split(".")[0].strip()
    best_motive, max_ratio = None, 0

    for motive in motives:
        name = motive.name.lower()
        ratio = fuzz.ratio(input_name, name)
        if ratio > MIN_SIMILARITY_THRESHOLD and ratio > max_ratio:
            best_motive, max_ratio = motive, ratio

    if best_motive:
        logger.info(f"Fuzzy match found: {best_motive.name} with score {max_ratio}")
    return best_motive


def match_using_llm(motives: List[Motive], input_name: str) -> Optional[Motive]:
    client = OpenAI(api_key=OPENAI_API_KEY)
    motive_list = [{"id": m.id, "name": m.name.lower()} for m in motives if m.name]

    logger.info("Using OpenAI to find motive")

    prompt = f"""
    Compte tenu des informations suivantes :
    Motif recherché : {input_name}

    Et les données suivantes :
    Motifs de visite : {json.dumps(motive_list)}

    Identifier l'ID qui correspond au motif désiré même s'il ressemble seulement un peu :
    Format attendu (JSON uniquement) :
    {{"id": 123456, "name": "motive_name"}}
    Si aucun motif ne correspond, renvoyez None.
    """

    try:
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "Vous êtes un assistant médical."},
                {"role": "user", "content": prompt},
            ],
        )
        raw_response = response.choices[0].message.content
        result = json.loads(extract_json_from_string(raw_response))
        return (
            match_by_id(motives, int(result["id"]))
            if result and "id" in result
            else None
        )
    except Exception as e:
        logger.error(f"Error during LLM-based motive matching: {e}")
        return None


def normalize_name(name: str) -> str:
    name = name.split("(")[0].strip().lower()
    return unicodedata.normalize("NFKD", name).encode("ASCII", "ignore").decode("utf-8")


def names_match(name1: str, name2: str) -> bool:
    n1, n2 = normalize_name(name1), normalize_name(name2)
    return (
        n1 == n2
        or " ".join(reversed(n1.split())) == n2
        or n1 == " ".join(reversed(n2.split()))
    )


def find_visit_motive_id_by_medecin(
    config: str, motive_name: str, medecin: str
) -> Optional[Motive]:
    company_data = supabase_client.get_company_data_by_config_id(config)
    config_data = company_data.inbound_config_file
    if not config_data:
        raise ValueError("Config data is not available or incomplete.")
    visit_motives_data = config_data.visit_motives_categories

    if isinstance(medecin, int):
        logger.warning("Medecin is an int, converting to str")
        medecin = str(medecin)

    find_doctor = None
    if medecin.isdigit():
        calendar = next(
            (c for c in config_data.calendars if str(c.id) == medecin),
            None,
        )
        if calendar:
            find_doctor = calendar
        else:
            logger.warning("Medecin ID not found in calendars")
            return None
    else:
        doctors = config_data.calendars
        find_doctor = find_doctor_by_name(medecin, doctors)

    if not find_doctor:
        logger.warning("Medecin name not resolved")
        return None

    visit_motives = [
        m for m in visit_motives_data if m.medecin and "(tous)" not in m.name
    ]

    if not visit_motives or not visit_motives[0].medecin:
        logger.warning("No 'medecin' field in visit motives")
        return None

    motive_name_stripped = motive_name.lower().split(".")[0].strip()
    best_match, best_score = None, 0
    for motive in visit_motives:
        if motive.medecin and not names_match(motive.medecin, find_doctor.name):
            continue
        score = fuzz.ratio(motive_name_stripped, motive.name)
        if score > best_score:
            best_score, best_match = score, motive

    if best_match and best_score > MIN_SIMILARITY_THRESHOLD:
        logger.info(f"Found direct or fuzzy match: {motive_name_stripped}")
        return next(
            (m for m in visit_motives_data if m.id == best_match.id),
            None,
        )

    logger.info("No high-confidence match found, using OpenAI fallback")
    return llm_match_motive_by_medecin(
        config, visit_motives, motive_name, find_doctor, best_match
    )


def llm_match_motive_by_medecin(
    config: str,
    motives: list,
    motive_name: str,
    medecin: Doctor,
    best_motive: Optional[Motive],
) -> Optional[Motive]:
    client = OpenAI(api_key=OPENAI_API_KEY)
    best_guess = (
        f"Je pense que c'est celui là: {best_motive.name}" if best_motive else ""
    )

    prompt = f"""
    Motif recherché : '{motive_name}' et médecin : '{medecin.name}'
    Motifs de visite : {json.dumps(motives, ensure_ascii=False)}
    {best_guess}

    Identifier le bon ID, ou None si non trouvé.
    Format attendu : {{"id": 123456, "name": "motive_name", "medecin": "medecin_name"}}
    """

    try:
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {
                    "role": "system",
                    "content": "Assistant pour trouver le bon motif et médecin.",
                },
                {"role": "user", "content": prompt},
            ],
        )
        raw_response = response.choices[0].message.content
        result = json.loads(extract_json_from_string(raw_response))
        return (
            match_by_id(motives, int(result["id"]))
            if result and "id" in result
            else None
        )
    except Exception as e:
        logger.error(f"LLM parsing error: {e}")
        return None
