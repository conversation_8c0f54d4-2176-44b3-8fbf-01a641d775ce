from typing import Optional

from models import CompanyData, Speciality


def find_speciality_by_id(
    company_data: CompanyData, speciality_id: int
) -> Optional[Speciality]:
    """
    Find a speciality by its ID in the company data.
    """
    for speciality in company_data.inbound_config_file.specialities:
        if speciality.id == speciality_id:
            return speciality

    return None
