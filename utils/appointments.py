from datetime import datetime
from typing import List
from zoneinfo import ZoneInfo

from loguru import logger

from booking_provider.BookingProvider import BookingProvider
from models import Appointment, AppointmentSearch

from .doctors import find_doctor_by_id_company_data
from .motives import find_visit_motive


def parse_availabilities_body(
    booking_provider: BookingProvider, body: dict
) -> AppointmentSearch:
    # Check agendas
    agenda_ids = body.get("agenda_ids", [])
    if not agenda_ids:
        logger.warning("No agenda IDs provided, using 'agenda_id' key instead.")
        agenda_ids = body.get("agenda_id", [])

    if not agenda_ids:
        logger.error("No agenda IDs provided in the request body.")
        raise ValueError("No agenda IDs provided")

    if any(not isinstance(agenda_id, int) for agenda_id in agenda_ids):
        logger.info("Converting agenda IDs to integers.")
        agenda_ids = [int(agenda_id) for agenda_id in agenda_ids]

    # Check days
    day = body.get("day")

    # paris time
    now = datetime.now(ZoneInfo("Europe/Paris"))
    if isinstance(day, str):
        try:
            day = datetime.strptime(day, "%Y-%m-%d").date()
        except ValueError:
            logger.error(f"Invalid date format: {day}")
            raise ValueError("Invalid date format")

    if day is None or day < now.date():
        logger.warning(
            "Date de début is None or in the past, using current date instead."
        )
        day = now.date()

    # Check motive
    visit_motive_id = body.get("visit_motive_id")
    if not visit_motive_id:
        logger.error("Visit motive ID not provided in the request body.")
        raise ValueError("Visit motive ID is required")

    motive = find_visit_motive(booking_provider.company_data, visit_motive_id)

    if not motive:
        logger.error(
            f"Motive with ID {visit_motive_id} not found in config {booking_provider.config_id}"
        )
        raise ValueError("Motive not found")

    # Check doctors
    doctors = []
    for agenda_id in agenda_ids:
        try:
            doctor = find_doctor_by_id_company_data(
                booking_provider.company_data, str(agenda_id)
            )
            if not doctor:
                continue  # Skip if doctor not found

            doctors.append(doctor)
        except Exception as e:
            logger.error(f"Error finding doctor for agenda_id {agenda_id}: {str(e)}")
            continue

    return AppointmentSearch(
        doctors=doctors,
        day=day,
        motive=motive,
    )


def find_consecutive_combinations(
    appointments_1: List[Appointment],
    appointments_2: List[Appointment],
    min_wait_between_steps=0,
    max_wait_between_steps=30,
):
    """
    Find all valid consecutive appointment combinations between different keys.

    Args:
        appointments_dict: Dictionary with keys containing lists of appointments
        min_wait_between_steps: Minimum wait time in minutes between appointments (default: 0)
        max_wait_between_steps: Maximum wait time in minutes between appointments (default: 30)

    Returns:
        List of valid combinations with no overlaps and within wait time constraints
    """

    # Check all combinations
    steps: List[List[Appointment]] = []
    for _, apt0 in enumerate(appointments_1):
        for _, apt1 in enumerate(appointments_2):
            # Calculate gap in minutes
            gap_minutes = (apt1.start_date - apt0.end_date).total_seconds() / 60

            # Check if appointments don't overlap AND meet wait time constraints
            if (
                apt0.end_date <= apt1.start_date
                and min_wait_between_steps <= abs(gap_minutes) <= max_wait_between_steps
            ):
                steps.append([apt0, apt1])

    return steps
