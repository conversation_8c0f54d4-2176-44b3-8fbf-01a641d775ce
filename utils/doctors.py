from typing import List

from rapidfuzz import fuzz

from lib.supabase_client import supabase_client
from models import CompanyData, Doctor


def find_doctor_by_id_company_data(
    company_data: CompanyData, agenda_id: int | str
) -> Doctor | None:
    if not company_data or not company_data.inbound_config_file:
        raise ValueError("Company data or inbound config file is not available.")

    doctors = company_data.inbound_config_file.calendars
    doctor = next(
        (
            doctor
            for doctor in doctors
            if str(doctor.id) == str(agenda_id)
            or str(doctor.external_id) == str(agenda_id)
        ),
        None,
    )

    return doctor


def find_doctor_by_id(config: str, agenda_id: int) -> Doctor:
    company_data = supabase_client.get_company_data_by_config_id(config)

    if not company_data or not company_data.inbound_config_file:
        raise ValueError("Company data or inbound config file is not available.")

    doctors = company_data.inbound_config_file.calendars
    find_doctor = next((doctor for doctor in doctors if doctor.id == agenda_id), None)

    if not find_doctor:
        raise ValueError(f"Doctor with ID {agenda_id} not found in company data.")

    return find_doctor


def normalize_name(name: str) -> str:
    return name.split("(")[0].strip().lower()


def find_doctor_by_name(name: str, doctors: List["Doctor"]):
    doctor = None
    max_ratio: float = 0
    input_name = normalize_name(name)

    for _doc in doctors:
        doc_name = normalize_name(_doc.name)
        # Try normal and swapped comparisons
        ratio1 = fuzz.ratio(doc_name, input_name)
        ratio2 = fuzz.ratio(" ".join(reversed(doc_name.split())), input_name)
        best_ratio = max(ratio1, ratio2)

        if best_ratio > max_ratio:
            max_ratio = best_ratio
            doctor = _doc

    return doctor
