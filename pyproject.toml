[project]
name = "booking-provider"
version = "0.1.0"
description = "Booking Provider"
readme = "README.md"
requires-python = ">=3.13"

dependencies = [
    "aiohappyeyeballs==2.6.1",
    "aiosignal==1.4.0",
    "annotated-types==0.7.0",
    "anyio==4.10.0",
    "attrs==25.3.0",
    "certifi==2025.8.3",
    "charset-normalizer==3.4.2",
    "click==8.2.1",
    "deprecation==2.1.0",
    "distro==1.9.0",
    "fastapi==0.116.1",
    "frozenlist==1.7.0",
    "gotrue==2.12.3",
    "h11==0.16.0",
    "h2==4.2.0",
    "hpack==4.1.0",
    "httpcore==1.0.9",
    "httpx==0.28.1",
    "hyperframe==6.1.0",
    "idna==3.10",
    "jiter==0.10.0",
    "loguru==0.7.3",
    "multidict==6.6.3",
    "openai==1.99.3",
    "packaging==25.0",
    "postgrest==1.1.1",
    "propcache==0.3.2",
    "pydantic==2.11.7",
    "pydantic-core==2.33.2",
    "pyjwt==2.10.1",
    "python-dateutil==2.9.0.post0",
    "python-dotenv==1.1.1",
    "rapidfuzz==3.13.0",
    "realtime==2.7.0",
    "requests==2.32.4",
    "six==1.17.0",
    "sniffio==1.3.1",
    "starlette==0.47.2",
    "storage3==0.12.1",
    "strenum==0.4.15",
    "supabase==2.18.0",
    "supafunc==0.10.1",
    "tqdm==4.67.1",
    "typing-extensions==4.14.1",
    "typing-inspection==0.4.1",
    "urllib3==2.5.0",
    "uvicorn==0.35.0",
    "websockets==15.0.1",
    "yarl==1.20.1",
]

[dependency-groups]
dev = [
    "black>=25.1.0",
    "isort>=6.0.1",
    "mypy>=1.17.0",
    "pydantic>=2.11.7",
    "ruff>=0.12.4",
    "types-python-dateutil>=2.9.0.20250822",
    "types-requests>=2.32.4.20250611",
]
