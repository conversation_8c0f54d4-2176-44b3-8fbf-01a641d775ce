from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException, Request
from fastapi.responses import JSONResponse
from loguru import logger

from booking_provider.BookingProvider import BookingProvider
from decorators.middleware_booking_provider import middleware_booking_provider
from models import NewPatient, UpdatePatient

router = APIRouter()


@router.get("", tags=["patient"])
async def get_patient(
    request: Request,
    booking_provider: BookingProvider = Depends(middleware_booking_provider),
):
    """
    Fetch a patient by their ID.
    """
    patient_id = request.query_params.get("id")

    # check in body
    if not patient_id:
        try:
            data = await request.json()
            patient_id = data.get("id")
        except Exception as e:
            logger.error(f"Patient not found in body or params: {str(e)}")

    if not patient_id:
        raise HTTPException(
            status_code=400, detail="Patient ID using `id` is required."
        )

    patient = booking_provider.get_patient(patient_id)

    if not patient:
        raise HTTPException(status_code=404, detail="Patient not found.")

    return patient


@router.post("", tags=["patient"])
async def create_patient(
    request: Request,
    booking_provider: BookingProvider = Depends(middleware_booking_provider),
):
    """Create a new patient in the system.
    The patient data is expected to be in the request body.
    Returns the created patient's ID.
    """
    patient_data = await request.json()

    if not patient_data:
        raise HTTPException(status_code=400, detail="Patient data is required.")

    if booking_provider.type == "EDL":
        # send 206 with body
        return JSONResponse(
            status_code=206,
            content={
                "message": "Patient cannot be created in EDL, it will directly be created when taking an appointment.",
                "patient": patient_data,
            },
        )

    try:
        new_patient = booking_provider.create_patient(NewPatient(**patient_data))
    except NotImplementedError as e:
        raise HTTPException(
            status_code=501,
            detail=str(e),
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"An error occurred while creating the patient: {str(e)}",
        )

    if not new_patient:
        raise HTTPException(status_code=500, detail="Failed to create patient.")

    return new_patient


@router.post("/search", tags=["patient"])
async def search_patient(
    request: Request,
    booking_provider: BookingProvider = Depends(middleware_booking_provider),
):
    """Search for a patient based on various parameters.
    Parameters can include patient_id, phone_number, first_name, and last_name.
    Returns a list of patients matching the search criteria.
    """
    # check if speciality is in params
    data = await request.json()
    patient_id = data.get("patient_id")
    phone_number = data.get("phone_number")
    first_name = data.get("first_name")
    last_name = data.get("last_name")
    string = data.get("string")

    if not phone_number:
        phone_number = string

    resp = booking_provider.search_patient(
        patient_id=patient_id,
        phone_number=phone_number,
        first_name=first_name,
        last_name=last_name,
    )

    if resp:
        return resp
    else:
        return HTTPException(
            status_code=404,
            detail="No patients found matching the search criteria.",
        )


@router.get("/history", tags=["patient"])
async def get_patient_history(
    request: Request,
    booking_provider: BookingProvider = Depends(middleware_booking_provider),
):
    """Fetch the appointment history of a patient by their ID."""
    patient_id = request.query_params.get("id")

    if not patient_id:
        try:
            data = await request.json()
            patient_id = data.get("id")
        except Exception as e:
            logger.error(f"Patient not found in body or params: {str(e)}")

    if not patient_id:
        raise HTTPException(
            status_code=400, detail="Patient ID using `id` is required."
        )

    appointments = booking_provider.get_appointments_of_patient(patient_id)

    if not appointments:
        raise HTTPException(
            status_code=404, detail="No appointments found for this patient."
        )

    return appointments


@router.put("", tags=["patient"])
async def update_patient(
    request: Request,
    booking_provider: BookingProvider = Depends(middleware_booking_provider),
):
    """Update an existing patient in the system.
    The patient data is expected to be in the request body.
    Returns the updated patient's ID.
    """
    patient_data = await request.json()

    if not patient_data:
        raise HTTPException(status_code=400, detail="Patient data is required.")

    try:
        updated_patient = booking_provider.update_patient(UpdatePatient(**patient_data))
    except NotImplementedError as e:
        raise HTTPException(
            status_code=501,
            detail=str(e),
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"An error occurred while updating the patient: {str(e)}",
        )

    if not updated_patient:
        raise HTTPException(status_code=500, detail="Failed to update patient.")

    return updated_patient
