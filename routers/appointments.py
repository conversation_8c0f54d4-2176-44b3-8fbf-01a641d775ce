from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Request
from loguru import logger

from booking_provider.BookingProvider import BookingProvider
from decorators.middleware_booking_provider import middleware_booking_provider
from models import AppointmentStepSearch, CombinedAppointments, NewAppointment
from utils.appointments import parse_availabilities_body
from utils.motives import find_visit_motive

router = APIRouter()


@router.post("/availabilities")
async def get_availabilities(
    request: Request,
    booking_provider: BookingProvider = Depends(middleware_booking_provider),
):
    try:
        body = await request.json()
        appointment_search = parse_availabilities_body(booking_provider, body)
        return booking_provider.get_availabilities(
            doctors=appointment_search.doctors,
            motive=appointment_search.motive,
            day=appointment_search.day,
        )
    except Exception as e:
        logger.error(f"Error fetching availabilities: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/availabilities-steps")
async def get_availabilities_steps(
    request: Request,
    booking_provider: BookingProvider = Depends(middleware_booking_provider),
):
    body = await request.json()
    raw_steps = body.get("steps", [])
    steps = (
        [AppointmentStepSearch(**raw_step) for raw_step in raw_steps]
        if isinstance(raw_steps, list)
        else []
    )
    day = body.get("day")
    if not steps:
        logger.error("No steps provided in the request body.")
        raise HTTPException(status_code=400, detail="No steps provided")

    try:
        day = datetime.strptime(day, "%Y-%m-%d").date()
    except ValueError:
        logger.error(f"Invalid date format: {day}")
        raise HTTPException(
            status_code=400, detail="Invalid date format, format in YYYY-MM-DD"
        )

    if len(steps) == 1:
        step = steps[0]
        appointment_search = parse_availabilities_body(
            booking_provider,
            {
                "agenda_ids": step.agenda_ids,
                "day": day,
                "visit_motive_id": step.visit_motive_id,
            },
        )

        appointments = booking_provider.get_availabilities(
            doctors=appointment_search.doctors,
            motive=appointment_search.motive,
            day=appointment_search.day,
        )
        appointments_steps = [
            CombinedAppointments(
                start_date=appointment.start_date,
                end_date=appointment.end_date,
                steps=[appointment],
                booking_provider=booking_provider.type,
                num_motives=len(steps),
            )
            for appointment in appointments
        ]
        return appointments_steps

    elif len(steps) == 2:
        min_wait_between_steps = (
            int(body.get("min_wait_between_steps", 0))
            if body.get("min_wait_between_steps")
            else 0
        )
        max_wait_between_steps = (
            int(body.get("max_wait_between_steps", 30))
            if body.get("max_wait_between_steps")
            else 30
        )
        return booking_provider.get_availabilities_mulitiple_motives(
            steps, day, min_wait_between_steps, max_wait_between_steps
        )
    else:
        return HTTPException(
            status_code=400, detail="Invalid number of steps can only be 1 or 2"
        )


@router.get("", tags=["appointments"])
async def get_appointment(
    request: Request,
    booking_provider: BookingProvider = Depends(middleware_booking_provider),
):
    appointment_id = request.query_params.get("id")

    if not appointment_id:
        try:
            data = await request.json()
            appointment_id = data.get("id")
        except Exception as e:
            logger.error(f"Error parsing request body: {str(e)}")
            raise HTTPException(status_code=400, detail="Invalid request body")

    if not appointment_id:
        logger.error("Appointment ID not provided in the request query.")
        raise HTTPException(status_code=400, detail="Appointment ID is required")

    appointments = booking_provider.get_appointment(appointment_id)
    return appointments


@router.post("")
async def create_appointment(
    request: Request,
    booking_provider: BookingProvider = Depends(middleware_booking_provider),
):
    body = await request.json()
    new_appointment = NewAppointment(**body)
    try:
        new_created_appointment = booking_provider.create_appointment(new_appointment)
        if not new_created_appointment:
            logger.error("Failed to create appointment, no data returned.")
            raise HTTPException(status_code=500, detail="Failed to create appointment")
        if new_created_appointment.steps and len(new_created_appointment.steps) == 0:
            motive = find_visit_motive(
                booking_provider.company_data,
                new_created_appointment.visit_motive_id,
                True,
            )
            new_created_appointment.visit_motive_name = motive.name if motive else None
            new_created_appointment.speciality_id = (
                motive.speciality_id if motive else None
            )
            new_created_appointment.instructions = (
                motive.instructions if motive else None
            )

        elif new_created_appointment.steps and len(new_created_appointment.steps) > 0:
            for step in new_created_appointment.steps:
                motive = find_visit_motive(
                    booking_provider.company_data,
                    step.visit_motive_id,
                    True,
                )
                step.visit_motive_name = motive.name if motive else None
                step.speciality_id = motive.speciality_id if motive else None
                step.instructions = motive.instructions if motive else None

            new_created_appointment.visit_motive_name = new_created_appointment.steps[
                0
            ].visit_motive_name
            new_created_appointment.speciality_id = new_created_appointment.steps[
                0
            ].speciality_id
            new_created_appointment.instructions = new_created_appointment.steps[
                0
            ].instructions
            new_created_appointment.speciality_id = new_created_appointment.steps[
                0
            ].speciality_id
            new_created_appointment.practitioner_id = new_created_appointment.steps[
                0
            ].practitioner_id
            new_created_appointment.instructions = new_created_appointment.steps[
                0
            ].instructions
            new_created_appointment.equipment_agenda_id = new_created_appointment.steps[
                0
            ].equipment_agenda_id
            new_created_appointment.end_date = new_created_appointment.steps[0].end_date
            new_created_appointment.booking_provider = booking_provider.type

        return new_created_appointment

    except Exception as e:
        logger.error(f"Error creating appointment: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("")
async def update_appointment(
    request: Request,
    booking_provider: BookingProvider = Depends(middleware_booking_provider),
):
    body = await request.json()
    appointment_id = body.get("id")
    if not appointment_id:
        logger.error("Appointment ID not provided in the request body.")
        raise HTTPException(status_code=400, detail="Appointment ID is required")

    try:
        updated_appointment = booking_provider.update_appointment(appointment_id, body)
        return updated_appointment
    except Exception as e:
        logger.error(f"Error updating appointment {appointment_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/confirm")
async def confirm_appointment(
    request: Request,
    booking_provider: BookingProvider = Depends(middleware_booking_provider),
):
    body = await request.json()
    appointment_id = body.get("id")
    notes = body.get("notes", "")

    if not appointment_id:
        logger.error("Appointment ID not provided in the request body.")
        raise HTTPException(status_code=400, detail="Appointment ID is required")

    try:
        confirmed_appointment = booking_provider.confirm_appointment(
            appointment_id, notes
        )
        return confirmed_appointment
    except Exception as e:
        logger.error(f"Error confirming appointment {appointment_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/cancel")
async def cancel_appointment(
    request: Request,
    booking_provider: BookingProvider = Depends(middleware_booking_provider),
):
    data = await request.json()
    appointment_id = data.get("id")

    if not appointment_id:
        logger.error("Appointment ID not provided in the request body.")
        raise HTTPException(status_code=400, detail="Appointment ID is required")

    notes = data.get("notes", "")

    try:
        return booking_provider.cancel_appointment(appointment_id, notes)

    except Exception as e:
        logger.error(f"Error canceling appointment {appointment_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
