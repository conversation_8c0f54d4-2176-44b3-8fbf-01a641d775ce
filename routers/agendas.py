from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Request
from loguru import logger

from booking_provider.BookingProvider import BookingProvider
from decorators.middleware_booking_provider import middleware_booking_provider

router = APIRouter()


@router.get("/day")
async def get_appointments_of_day(
    request: Request,
    booking_provider: BookingProvider = Depends(middleware_booking_provider),
):
    """
    Get all appointments for a specific day of a specific agenda
    """
    agenda_ids = request.query_params.get("agenda_ids")
    day = request.query_params.get("day")

    if not agenda_ids:
        body = await request.json()
        if not body:
            logger.error("No body provided in the request.")
            raise HTTPException(status_code=400, detail="No body provided")

        agenda_ids = body.get("agenda_ids") or body.get("agenda_id")
        day = body.get("day")

    if not agenda_ids:
        logger.error("No agenda IDs provided in the request.")
        raise HTTPException(status_code=400, detail="No agenda IDs provided")

    agenda_ids = agenda_ids.split(",") if isinstance(agenda_ids, str) else agenda_ids

    # Convert to integers
    try:
        agenda_ids = [int(id.strip()) for id in agenda_ids]
    except ValueError:
        logger.error("Invalid agenda IDs provided - must be integers.")
        raise HTTPException(
            status_code=400, detail="Invalid agenda IDs provided - must be integers"
        )

    if not day:
        logger.error("Day is required.")
        raise HTTPException(status_code=400, detail="Day is required")

    try:
        day = datetime.strptime(day, "%Y-%m-%d").date()
    except ValueError:
        logger.error(f"Invalid date format: {day}")
        raise HTTPException(status_code=400, detail="Invalid date format")

    try:
        appointments = booking_provider.get_appointments_of_day(agenda_ids, day)
    except Exception as e:
        logger.error(f"Error fetching appointments: {e}")
        raise HTTPException(status_code=500, detail=str(e))

    return appointments
