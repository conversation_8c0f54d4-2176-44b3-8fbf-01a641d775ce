from fastapi import APIRouter, Depends, Request

from booking_provider.BookingProvider import BookingProvider
from decorators.middleware_booking_provider import middleware_booking_provider
from lib.supabase_client import supabase_client
from utils.motives import find_visit_motive, find_visit_motive_id_by_medecin

router = APIRouter()


@router.get("", tags=["motives"])
def get_motives(
    request: Request,
    booking_provider: BookingProvider = Depends(middleware_booking_provider),
):
    """
    Fetches motives based on the provided speciality ID.
    """

    company_data = supabase_client.get_company_data_by_config_id(
        booking_provider.config_id
    )

    if not company_data:
        return {"error": "Company data not found"}, 404

    if not company_data.inbound_config_file:
        return {"error": "Inbound config file not found"}, 404

    motives = company_data.inbound_config_file.visit_motives_categories

    return motives


@router.post("", tags=["motives"])
async def get_motive_by_id_or_name(
    request: Request,
    booking_provider: BookingProvider = Depends(middleware_booking_provider),
):
    """
    Fetches a motive by its ID or name.
    """

    data = await request.json()

    practice = data.get("practice")
    medecin = data.get("medecin") if data.get("medecin") else None
    company_data = supabase_client.get_company_data_by_config_id(
        booking_provider.config_id
    )

    if medecin:
        motive = find_visit_motive_id_by_medecin(
            booking_provider.config_id, practice, medecin
        )
        if motive:
            return motive.model_dump()

    motive = find_visit_motive(company_data, practice)

    if not motive:
        return {"error": "Motive not found"}, 404

    return motive
