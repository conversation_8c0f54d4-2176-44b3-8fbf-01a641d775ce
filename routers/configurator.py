from typing import Optional

from fastapi import APIRouter, Depends, HTTPException, Request
from loguru import logger

from booking_provider.BookingProvider import BookingProvider
from decorators.middleware_booking_provider import middleware_booking_provider
from lib.supabase_client import supabase_client
from models import InboundConfig

router = APIRouter()


@router.get("/check", tags=["check"])
def check_config(
    request: Request,
    booking_provider: BookingProvider = Depends(middleware_booking_provider),
):
    """
    Check the configuration for the EDL service.
    """
    check_all = request.query_params.get("check_all", "false").lower() == "true"
    config_id = request.query_params.get("config_id")
    if not config_id:
        config_id = request.query_params.get("config")

    if config_id and not check_all:
        company_data = supabase_client.get_company_data_by_config_id(config_id)
        if not company_data or not company_data.inbound_config_file:
            raise HTTPException(status_code=404, detail="Config not found")
        InboundConfig(**company_data.inbound_config_file.model_dump())
        return {"message": "Config is valid", "config_id": config_id}

    config_results = []
    valid_count = 0
    invalid_count = 0
    list_of_invalid_configs = []

    company_data_list = supabase_client.get_companies_dict()
    for company_data in company_data_list:
        config_name = company_data.get("config_name")
        config = company_data.get("config")
        inbound_config_file = company_data.get("inbound_config_file")
        if inbound_config_file:
            try:
                InboundConfig(**inbound_config_file)
                valid_count += 1
            except Exception as e:
                config_results.append(
                    {"config": config_name, "status": "invalid", "error": str(e)}
                )
                invalid_count += 1
                list_of_invalid_configs.append(f"{config}: {config_name}")
        else:
            config_results.append(
                {
                    "config": config_name,
                    "status": "no_config_file",
                    "error": "No inbound_config_file found",
                }
            )
            invalid_count += 1
            list_of_invalid_configs.append(f"{config}: {config_name}")

    summary = {
        "total_configs": len(company_data_list),
        "valid_configs": valid_count,
        "invalid_configs": invalid_count,
        "results": config_results,
        "list_of_invalid_configs": list_of_invalid_configs,
    }

    if invalid_count == 0:
        return {"message": "All configs are valid", "summary": summary}
    else:
        return {
            "message": f"Found {invalid_count} invalid config(s) out of {len(config_results)} total",
            "summary": summary,
        }


@router.get("/generate", tags=["generate"])
def generate_config(
    request: Request,
    booking_provider: BookingProvider = Depends(middleware_booking_provider),
):
    """
    Generate the configuration for the EDL service.
    """

    resp = booking_provider.generate_config()
    if not resp:
        raise Exception("Error generating configuration")
    return resp


@router.get("/patients", tags=["patients"])
def get_patients(
    patient_id: Optional[str] = None,
    phone_number: Optional[str] = None,
    first_name: Optional[str] = None,
    last_name: Optional[str] = None,
    booking_provider: BookingProvider = Depends(middleware_booking_provider),
):
    """
    Search for patients in the EDL service.
    """

    resp = booking_provider.search_patient(
        patient_id=patient_id,
        phone_number=phone_number,
        first_name=first_name,
        last_name=last_name,
        raw=True,
    )

    if not resp:
        raise Exception("Error searching for patients")
    return resp


@router.get("/specialities", tags=["specialities"])
def get_specialities(
    booking_provider: BookingProvider = Depends(middleware_booking_provider),
):
    resp = booking_provider.get_original_specialities()
    if not resp:
        raise Exception("Error retrieving specialities")
    return resp


@router.get("/motives", tags=["motives"])
def get_motives(
    speciality_id: Optional[int] = None,
    booking_provider: BookingProvider = Depends(middleware_booking_provider),
):
    """
    Fetch motives from the EDL service, optionally filtered by speciality ID.
    """
    try:
        resp = booking_provider.get_original_motives(speciality_id=speciality_id)
        if not resp:
            raise Exception("Error retrieving motives")
        return resp
    except Exception as e:
        logger.error(f"Error fetching motives: {e}")
        return HTTPException(status_code=500, detail=str(e))


@router.get("/doctors", tags=["doctors"])
def get_doctors(
    booking_provider: BookingProvider = Depends(middleware_booking_provider),
):
    """
    Fetch doctors from the EDL service.
    """

    data = booking_provider.get_original_doctors()

    if not data:
        raise Exception("Error retrieving doctors")

    return data
