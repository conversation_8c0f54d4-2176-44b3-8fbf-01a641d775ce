from datetime import datetime, timed<PERSON>ta
from zoneinfo import ZoneInfo

import jwt
from fastapi import APIRouter, Depends, HTTPException, Request
from loguru import logger

from booking_provider import get_booking_provider
from booking_provider.BookingProvider import BookingProvider
from constants import JWT_SECRET_KEY
from decorators.middleware_booking_provider import middleware_jwt_auth
from lib.supabase_client import supabase_client
from models import JWTBookingPayload, UpdatePatient

router = APIRouter()


@router.get("/auth/{booking_id}")
def get_no_auth_booking(booking_id):
    """
    Get appointment details from vocca
    """
    try:
        booking = supabase_client.get_booking(booking_id)
        full_name = booking.full_name

        parts = full_name.split()
        if len(parts) > 1:
            full_name = " ".join([part[0] + "*" * (len(part) - 1) for part in parts])
        else:
            full_name = full_name[0] + "*" * (len(full_name) - 1)

        return {
            "booking_id": booking_id,
            "full_name": full_name,
            "config_id": booking.config_id,
        }
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/auth/{booking_id}")
async def get_jwt_token_for_booking(request: Request, booking_id: str):
    """
    Get JWT token for the booking
    """
    try:
        data = await request.json()
        if not data:
            raise HTTPException(status_code=400, detail="Request body is required")

        birthdate = data.get("birthdate")
        if not birthdate or not isinstance(birthdate, str):
            raise HTTPException(status_code=400, detail="birthdate is required")

        # Clean and normalize the birthdate
        try:
            birthdate = datetime.strptime(birthdate.strip(), "%Y-%m-%d").date()
        except ValueError:
            raise HTTPException(
                status_code=400, detail="Invalid birthdate format. Use YYYY-MM-DD"
            )

        booking = supabase_client.get_booking(booking_id)
        appointment_id = booking.appointment_id
        config_id = booking.config_id

        booking_provider = get_booking_provider(config_id)
        if not booking_provider:
            raise HTTPException(status_code=404, detail="Booking provider not found")

        appointment = booking_provider.get_appointment(appointment_id)

        patient = booking_provider.get_patient(appointment.patient_id)

        if not patient.birthdate:
            raise HTTPException(
                status_code=404, detail="Birthdate not found in patient data"
            )

        if birthdate != patient.birthdate:
            raise HTTPException(status_code=401, detail="Birthdate does not match")

        # Generate JWT token
        now = datetime.now()
        try:
            payload = {
                "appointment_id": appointment_id,
                "config_id": config_id,
                "booking_id": booking_id,
                "exp": now + timedelta(hours=1),  # 1 hour expiration
                "iat": now,
            }

            token = jwt.encode(payload, JWT_SECRET_KEY, algorithm="HS256")

            return {
                "token": token,
                "expires_in": 3600,  # 1 hour in seconds
                "patient_data": {
                    "first_name": patient.first_name,
                    "last_name": patient.last_name,
                },
                "appointment_id": appointment_id,
            }

        except Exception as e:
            logger.error(f"Error generating JWT token: {str(e)}")
            raise HTTPException(status_code=500, detail="Authentication failed")

    except Exception as e:
        logger.error(f"Error in create_booking: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{booking_id}")
async def get_booking(
    booking_id: str,
    booking_provider_jwt_data: tuple[BookingProvider, JWTBookingPayload] = Depends(
        middleware_jwt_auth
    ),
):
    """
    Get booking details by ID
    """
    booking_provider, jwt_data = booking_provider_jwt_data
    try:
        booking = supabase_client.get_booking(booking_id)
        company_data = supabase_client.get_company_data_by_config_id(booking.config_id)
        if (
            jwt_data.booking_id != booking.id
            or jwt_data.appointment_id != booking.appointment_id
        ):
            raise HTTPException(
                status_code=403, detail="Unauthorized access to this booking"
            )

        appointment = booking_provider.get_appointment(booking.appointment_id)
        if not appointment:
            raise HTTPException(status_code=404, detail="Appointment not found")

        appointment = booking_provider.get_appointment(booking.appointment_id)

        return {
            "booking": booking,
            "appointment": appointment,
            "company_data": {
                "email": company_data.email,
                "phone": company_data.phone_number_center,
                "name": company_data.name,
                "address": company_data.address,
                "opening_hours": company_data.openings_2,
            },
        }
    except ValueError as e:
        logger.error(f"Error fetching appointment: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{booking_id}")
async def delete_booking(
    request: Request,
    booking_id: str,
    booking_provider_jwt_data: tuple[BookingProvider, JWTBookingPayload] = Depends(
        middleware_jwt_auth
    ),
):
    try:
        data = await request.json()
        booking_provider, _ = booking_provider_jwt_data

        notes = data.get("notes", "")
        appointment_id = supabase_client.get_appointment_id_from_booking_id(booking_id)

        appointment = booking_provider.get_appointment(appointment_id)

        now = datetime.now(ZoneInfo("Europe/Paris"))

        if (
            "no_show" in appointment.status.lower()
            or "delete" in appointment.status.lower()
        ):
            raise HTTPException(status_code=400, detail="Appointment already cancelled")

        if appointment.start_date < now:
            raise HTTPException(
                status_code=400, detail="Cannot cancel past appointments"
            )

        patient = booking_provider.get_patient(appointment.patient_id)

        if not patient.birthdate:
            raise HTTPException(
                status_code=404, detail="Birthdate not found in patient data"
            )
        if not appointment.id:
            raise HTTPException(status_code=404, detail="Appointment ID not found")

        booking_provider.cancel_appointment(appointment.id, notes)
        return {"message": "Appointment cancelled successfully"}

    except HTTPException as http_exc:
        logger.error(f"HTTP Exception: {str(http_exc.detail)}")
        raise http_exc

    except Exception as e:
        logger.error(f"Error cancelling appointment: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/confirm")
async def confirm_appointment(
    request: Request,
    booking_provider_jwt_data: tuple[BookingProvider, JWTBookingPayload] = Depends(
        middleware_jwt_auth
    ),
):
    try:
        data = await request.json()
        booking_provider, jwt_data = booking_provider_jwt_data
        appointment = booking_provider.get_appointment(jwt_data.appointment_id)

        if not appointment or not appointment.id:
            raise HTTPException(status_code=404, detail="Appointment not found")

        booking_provider.confirm_appointment(appointment.id, data.get("notes", ""))
        return {"message": "Appointment confirmed successfully"}

    except HTTPException as http_exc:
        logger.error(f"HTTP Exception: {str(http_exc.detail)}")
        raise http_exc

    except Exception as e:
        logger.error(f"Error confirming appointment: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/update-info")
async def update_patient_info(
    request: Request,
    booking_provider_jwt_data: tuple[BookingProvider, JWTBookingPayload] = Depends(
        middleware_jwt_auth
    ),
):
    try:
        data = await request.json()
        booking_provider, jwt_data = booking_provider_jwt_data
        appointment = booking_provider.get_appointment(jwt_data.appointment_id)

        if not appointment:
            raise HTTPException(status_code=404, detail="Appointment not found")

        patient = booking_provider.get_patient(appointment.patient_id)

        # Update patient information based on provided data
        for key, value in data.items():
            if key in [
                "email",
                "address",
                "zipcode",
                "city",
                "phone",
                "first_name",
                "last_name",
            ]:
                setattr(patient, key, value)

        booking_provider.update_patient(
            UpdatePatient(
                **patient.model_dump(),
            )
        )
        return {"message": "Patient information updated successfully"}

    except HTTPException as http_exc:
        logger.error(f"HTTP Exception: {str(http_exc.detail)}")
        raise http_exc

    except Exception as e:
        logger.error(f"Error updating patient information: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
