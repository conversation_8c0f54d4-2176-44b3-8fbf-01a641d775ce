from fastapi import APIRouter, Depends, Request

from booking_provider.BookingProvider import BookingProvider
from decorators.middleware_booking_provider import middleware_booking_provider

router = APIRouter()


@router.get("")
async def health_check(
    _: Request,
    booking_provider: BookingProvider = Depends(middleware_booking_provider),
):
    booking_provider.health_check()
    return {"status": "healthy"}
