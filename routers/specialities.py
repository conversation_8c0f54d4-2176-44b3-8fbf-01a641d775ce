from fastapi import APIRouter, Depends, Request

from booking_provider.BookingProvider import BookingProvider
from decorators.middleware_booking_provider import middleware_booking_provider
from lib.supabase_client import supabase_client

router = APIRouter()


@router.get("", tags=["specialities"])
def get_specialities(
    request: Request,
    booking_provider: BookingProvider = Depends(middleware_booking_provider),
):
    """
    Fetches specialities based on the provided config ID.
    """

    company_data = supabase_client.get_company_data_by_config_id(
        booking_provider.config_id
    )

    if not company_data:
        return {"error": "Company data not found"}, 404

    if not company_data.inbound_config_file:
        return {"error": "Inbound config file not found"}, 404

    specialities = company_data.inbound_config_file.specialities

    return specialities
