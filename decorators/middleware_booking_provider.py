import jwt
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, Request
from loguru import logger

from booking_provider import get_booking_provider
from booking_provider.BookingProvider import BookingProvider
from constants import JWT_SECRET_KEY
from models import JWTBookingPayload

if not JWT_SECRET_KEY:
    raise ValueError("JWT_SECRET_KEY is when loading middleware_booking_provider")


# You may need to import these constants if they exist in your constants file
# from constants import ADMIN_TOKEN, JWT_SECRET_KEY, OLD_ADMIN_TOKEN
async def middleware_booking_provider(request: Request) -> BookingProvider:
    """
    FastAPI dependency to extract and validate token and config.
    This is the recommended FastAPI way to handle authentication.
    """
    config_id = request.headers.get("Config-id")

    config_id = None

    # First check headers
    config_id = request.headers.get("Config-id")

    # If not in headers, check request data

    data = {}
    try:
        if request.headers.get("content-type") == "application/json":
            data = await request.json()
    except Exception:
        pass

    # If no JSON data, use query parameters
    if not data:
        data = dict(request.query_params)

    # Try 'config' first, then 'config_id'
    config_data_id = data.get("config") or data.get("config_id")

    if config_data_id and config_id and str(config_data_id) != str(config_id):
        raise HTTPException(
            status_code=400, detail="Config ID mismatch between header and data!"
        )

    if config_data_id and not config_id:
        config_id = config_data_id

    if not config_id:
        raise HTTPException(status_code=400, detail="'config': str is missing!")

    if not isinstance(config_id, str) or not config_id.strip():
        raise HTTPException(
            status_code=400, detail="'config' must be a non-empty string!"
        )
    # Check for token in Authorization header
    token = None
    auth_header = request.headers.get("Authorization")
    if auth_header and auth_header.startswith("Bearer "):
        token = auth_header.split(" ", 1)[1]

    # Check for token in request data
    if not token and "token" in data:
        token = data.get("token")

    if not token:
        raise HTTPException(status_code=401, detail="Token is missing!")

    # Replace this with your actual token validation logic
    # if token != ADMIN_TOKEN and token != OLD_ADMIN_TOKEN:
    if token != "expected_token_value":
        raise HTTPException(status_code=401, detail="Invalid token!")

    booking_provider = get_booking_provider(config_id)

    if not booking_provider:
        raise HTTPException(
            status_code=404,
            detail=f"Booking provider not found for the given config ID: {config_id}",
        )

    return booking_provider


async def middleware_jwt_auth(
    request: Request,
) -> tuple[BookingProvider, JWTBookingPayload]:
    """
    FastAPI dependency to extract and validate JWT token.
    This is the recommended FastAPI way to handle authentication.
    """
    # Try to get data from JSON body first, then query parameters
    data = {}
    try:
        if request.headers.get("content-type") == "application/json":
            data = await request.json()
    except Exception:
        pass

    # If no JSON data, use query parameters
    if not data:
        data = dict(request.query_params)

    # Check for JWT token in Authorization header
    token = None
    auth_header = request.headers.get("Authorization")
    if auth_header and auth_header.startswith("Bearer "):
        token = auth_header.split(" ", 1)[1]

    if not token:
        raise HTTPException(status_code=401, detail="Token is missing!")

    try:
        payload = jwt.decode(
            token,
            JWT_SECRET_KEY,
            algorithms=["HS256"],
            leeway=10800,  # 3 hours in seconds
        )

        booking_payload = JWTBookingPayload(**payload)
        booking_provider = get_booking_provider(payload.get("config_id"))

        if not booking_provider:
            raise HTTPException(
                status_code=404,
                detail=f"Booking provider not found for the given config ID: {booking_payload.config_id}",
            )

        return booking_provider, booking_payload

    except jwt.ExpiredSignatureError as e:
        logger.error(f"JWT token has expired: {str(e)}")
        raise HTTPException(status_code=401, detail="Token has expired")
    except jwt.InvalidTokenError as e:
        logger.error(f"JWT token is invalid: {str(e)}")
        raise HTTPException(status_code=401, detail="Invalid token")
    except Exception as e:
        logger.error(f"JWT verification error: {str(e)}")
        logger.error(f"Exception type: {type(e).__name__}")
        raise HTTPException(status_code=401, detail="Token verification failed")
